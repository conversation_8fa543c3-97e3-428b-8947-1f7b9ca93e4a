/**
 * Changelog Page - Business-friendly changelog display.
 * 
 * Following their established patterns with server-side rendering,
 * tenant-aware routing, and business-focused presentation.
 */

import { DashboardLayout } from "@/components/dashboard/dashboard-layout";
import { ChangelogList } from "@/features/changelog/components/server/changelog-list";
import { ChangelogHeader } from "@/features/changelog/components/server/changelog-header";
import { ChangelogFilters } from "@/features/changelog/components/client/changelog-filters";

interface ChangelogPageProps {
  params: Promise<{
    tenantSlug: string;
  }>;
  searchParams: Promise<{
    entry_type?: string;
    impact_level?: string;
    tags?: string;
    featured_only?: string;
    page?: string;
  }>;
}

export default async function ChangelogPage({ 
  params, 
  searchParams 
}: ChangelogPageProps) {
  const { tenantSlug } = await params;
  const filters = await searchParams;

  return (
    <DashboardLayout tenantSlug={tenantSlug}>
      <div className="space-y-6">
        {/* Header with title and description */}
        <ChangelogHeader tenantSlug={tenantSlug} />
        
        {/* Filters */}
        <ChangelogFilters 
          tenantSlug={tenantSlug}
          currentFilters={filters}
        />
        
        {/* Changelog entries list */}
        <ChangelogList 
          tenantSlug={tenantSlug}
          filters={filters}
        />
      </div>
    </DashboardLayout>
  );
}
