{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/auth-config.ts"], "sourcesContent": ["// lib/auth-config.ts - Clean enterprise configuration\nexport const AUTH_CONFIG = {\n  API_BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',\n} as const;\n\n// Enterprise session configuration\nexport const SESSION_CONFIG = {\n  COOKIE_NAME: 'auth_token',\n  MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours in milliseconds\n  SECURE: process.env.NODE_ENV === 'production',\n  SAME_SITE: 'lax' as const,\n} as const;\n  \n  // lib/api-client.ts\n  interface AuthResponse {\n    token: string\n    user: {\n      id: number\n      email: string\n      firstName: string\n      lastName: string\n      role: string\n      createdAt: string\n      updatedAt: string\n    }\n    client: {\n      id: number\n      slug: string\n      name: string\n      websiteUrl: string\n      industry: string\n    }\n  }\n  \n  interface LoginData {\n    email: string\n    password: string\n  }\n  \n  interface RegisterData {\n    first_name: string\n    last_name: string\n    email: string\n    password: string\n    company_name: string\n    website_url?: string\n    industry?: string\n  }\n  \n  class ApiClient {\n    private baseUrl: string\n  \n    constructor(baseUrl: string) {\n      this.baseUrl = baseUrl\n    }\n  \n    private async request<T>(\n      endpoint: string,\n      options: RequestInit = {}\n    ): Promise<T> {\n      const url = `${this.baseUrl}${endpoint}`\n      \n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      }\n  \n      // Add auth token if available\n      if (typeof window !== 'undefined') {\n        const token = localStorage.getItem(AUTH_CONFIG.API_BASE_URL)\n        if (token) {\n          config.headers = {\n            ...config.headers,\n            Authorization: `Token ${token}`,\n          }\n        }\n      }\n  \n      const response = await fetch(url, config)\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)\n      }\n  \n      return response.json()\n    }\n  \n    async login(data: LoginData): Promise<AuthResponse> {\n      return this.request<AuthResponse>('/api/auth/login/', {\n        method: 'POST',\n        body: JSON.stringify(data),\n      })\n    }\n  \n    async register(data: RegisterData): Promise<AuthResponse> {\n      return this.request<AuthResponse>('/api/auth/register/', {\n        method: 'POST',\n        body: JSON.stringify(data),\n      })\n    }\n  \n    async logout(): Promise<void> {\n      await this.request('/api/auth/logout/', {\n        method: 'POST',\n      })\n    }\n  \n    async refreshToken(): Promise<{ token: string }> {\n      return this.request<{ token: string }>('/api/auth/refresh/', {\n        method: 'POST',\n      })\n    }\n  }\n  \n  export const apiClient = new ApiClient(AUTH_CONFIG.API_BASE_URL)\n  "], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AAC/C,MAAM,cAAc;IACzB,cAAc,6DAAmC;AACnD;AAGO,MAAM,iBAAiB;IAC5B,aAAa;IACb,SAAS,KAAK,KAAK,KAAK;IACxB,QAAQ,oDAAyB;IACjC,WAAW;AACb;AAsCE,MAAM;IACI,QAAe;IAEvB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,8BAA8B;QAC9B;;QAUA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC9E;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAM,IAAe,EAAyB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAe,oBAAoB;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAS,IAAkB,EAAyB;QACxD,OAAO,IAAI,CAAC,OAAO,CAAe,uBAAuB;YACvD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAwB;QAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB;YACtC,QAAQ;QACV;IACF;IAEA,MAAM,eAA2C;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAoB,sBAAsB;YAC3D,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI,UAAU,YAAY,YAAY", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/auth/server.ts"], "sourcesContent": ["// lib/auth/server.ts - Fixed export\nimport { cookies } from 'next/headers';\nimport { AUTH_CONFIG, SESSION_CONFIG } from '../auth-config';\n\nexport interface ServerSession {\n  user: {\n    id: number;\n    email: string;\n    firstName: string;\n    lastName: string;\n    role: string;\n  };\n  client: {\n    id: number;\n    slug: string;\n    name: string;\n    websiteUrl: string;\n    industry: string;\n  };\n  token: string;\n}\n\n/**\n * Get authenticated session from HTTP-only cookies\n * No localStorage dependencies\n */\nexport async function getServerSession(): Promise<ServerSession | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get(SESSION_CONFIG.COOKIE_NAME)?.value;\n\n    if (!token) {\n      return null;\n    }\n\n    return await validateTokenWithBackend(token);\n  } catch (error) {\n    console.error('Server session validation failed:', error);\n    return null;\n  }\n}\n\n/**\n * Validate token with Django backend\n * ✅ FIXED: Now exported for middleware use\n */\nexport async function validateTokenWithBackend(token: string): Promise<ServerSession | null> {\n  try {\n    const controller = new AbortController();\n    const timeout = setTimeout(() => controller.abort(), 10000);\n\n    const response = await fetch(`${AUTH_CONFIG.API_BASE_URL}/api/auth/validate/`, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Token ${token}`,\n        'Content-Type': 'application/json',\n      },\n      cache: 'no-store',\n      signal: controller.signal,\n    });\n\n    clearTimeout(timeout);\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const data = await response.json();\n    \n    return {\n      user: data.user,\n      client: data.client,\n      token,\n    };\n  } catch (error) {\n    console.error('Token validation failed:', error);\n    return null;\n  }\n}\n\n/**\n * Validate tenant access for fortress-level security\n */\nexport async function validateTenantAccess(\n  session: ServerSession,\n  tenantSlug: string\n): Promise<boolean> {\n  return session.client.slug === tenantSlug;\n}\n\n/**\n * Require authentication with tenant validation\n */\nexport async function requireAuthentication(tenantSlug: string): Promise<ServerSession> {\n  const session = await getServerSession();\n  \n  if (!session) {\n    throw new Error('Authentication required');\n  }\n\n  const validTenant = await validateTenantAccess(session, tenantSlug);\n  if (!validTenant) {\n    throw new Error('Tenant access denied');\n  }\n\n  return session;\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;AACpC;AACA;;;AAwBO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,8PAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,4HAAA,CAAA,iBAAc,CAAC,WAAW,GAAG;QAE3D,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO,MAAM,yBAAyB;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAMO,eAAe,yBAAyB,KAAa;IAC1D,IAAI;QACF,MAAM,aAAa,IAAI;QACvB,MAAM,UAAU,WAAW,IAAM,WAAW,KAAK,IAAI;QAErD,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,cAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,MAAM,EAAE,OAAO;gBACjC,gBAAgB;YAClB;YACA,OAAO;YACP,QAAQ,WAAW,MAAM;QAC3B;QAEA,aAAa;QAEb,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;YACnB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAKO,eAAe,qBACpB,OAAsB,EACtB,UAAkB;IAElB,OAAO,QAAQ,MAAM,CAAC,IAAI,KAAK;AACjC;AAKO,eAAe,sBAAsB,UAAkB;IAC5D,MAAM,UAAU,MAAM;IAEtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,qBAAqB,SAAS;IACxD,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/tenant-client-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TenantClientProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TenantClientProvider() from the server but TenantClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/tenant-client-provider.tsx <module evaluation>\",\n    \"TenantClientProvider\",\n);\nexport const useTenant = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTenant() from the server but useTenant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/tenant-client-provider.tsx <module evaluation>\",\n    \"useTenant\",\n);\nexport const useTenantApi = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTenantApi() from the server but useTenant<PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/tenant-client-provider.tsx <module evaluation>\",\n    \"useTenantApi\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,qFACA;AAEG,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qFACA;AAEG,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qFACA", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/tenant-client-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TenantClientProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TenantClientProvider() from the server but TenantClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/tenant-client-provider.tsx\",\n    \"TenantClientProvider\",\n);\nexport const useTenant = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTenant() from the server but useTenant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/tenant-client-provider.tsx\",\n    \"useTenant\",\n);\nexport const useTenantApi = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTenantApi() from the server but useTenantA<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/tenant-client-provider.tsx\",\n    \"useTenantApi\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,iEACA;AAEG,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA;AAEG,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iEACA", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/tenant-provider.tsx"], "sourcesContent": ["// components/providers/tenant-provider.tsx - FIXED\r\nimport { ReactNode } from \"react\";\r\nimport { TenantClientProvider } from \"@/components/providers/tenant-client-provider\";\r\nimport { TenantSession } from \"@/types/tenant\";\r\n\r\ninterface TenantProviderProps {\r\n  children: ReactNode;\r\n  session: TenantSession;\r\n  tenantSlug: string;\r\n}\r\n\r\n/**\r\n * Server-side tenant provider that validates session and passes\r\n * secure tenant context to client components\r\n */\r\nexport async function TenantProvider({\r\n  children,\r\n  session,\r\n  tenantSlug,\r\n}: TenantProviderProps) {\r\n  // Server-side validation - this runs on every request\r\n  if (session.client.slug !== tenantSlug) {\r\n    throw new Error(\"Tenant access violation detected\");\r\n  }\r\n\r\n  // Calculate permissions server-side\r\n  const isOwner = session.user.role === \"owner\";\r\n\r\n  // ✅ FIXED: Calculate permissions and pass as data, not function\r\n  const rolePermissions = {\r\n    admin: [\"analytics\", \"keywords\", \"competitors\", \"settings\"],\r\n    manager: [\"analytics\", \"keywords\", \"competitors\"],\r\n    viewer: [\"analytics\"],\r\n  };\r\n\r\n  const userPermissions = isOwner\r\n    ? [\"analytics\", \"keywords\", \"competitors\", \"settings\", \"billing\", \"team\"] // Owner gets everything\r\n    : rolePermissions[session.user.role as keyof typeof rolePermissions] || [];\r\n\r\n  // ✅ FIXED: Pass data only, no functions\r\n  const contextValue = {\r\n    session,\r\n    tenantSlug,\r\n    isOwner,\r\n    userRole: session.user.role, // ← Pass role data\r\n    permissions: userPermissions, // ← Pass computed permissions array\r\n  };\r\n\r\n  return (\r\n    <TenantClientProvider value={contextValue}>{children}</TenantClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AAEnD;;;AAaO,eAAe,eAAe,EACnC,QAAQ,EACR,OAAO,EACP,UAAU,EACU;IACpB,sDAAsD;IACtD,IAAI,QAAQ,MAAM,CAAC,IAAI,KAAK,YAAY;QACtC,MAAM,IAAI,MAAM;IAClB;IAEA,oCAAoC;IACpC,MAAM,UAAU,QAAQ,IAAI,CAAC,IAAI,KAAK;IAEtC,gEAAgE;IAChE,MAAM,kBAAkB;QACtB,OAAO;YAAC;YAAa;YAAY;YAAe;SAAW;QAC3D,SAAS;YAAC;YAAa;YAAY;SAAc;QACjD,QAAQ;YAAC;SAAY;IACvB;IAEA,MAAM,kBAAkB,UACpB;QAAC;QAAa;QAAY;QAAe;QAAY;QAAW;KAAO,CAAC,wBAAwB;OAChG,eAAe,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAiC,IAAI,EAAE;IAE5E,wCAAwC;IACxC,MAAM,eAAe;QACnB;QACA;QACA;QACA,UAAU,QAAQ,IAAI,CAAC,IAAI;QAC3B,aAAa;IACf;IAEA,qBACE,6WAAC,+JAAA,CAAA,uBAAoB;QAAC,OAAO;kBAAe;;;;;;AAEhD", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/navigation/tenant-navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TenantNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call TenantNavigation() from the server but TenantNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation/tenant-navigation.tsx <module evaluation>\",\n    \"TenantNavigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iFACA", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/navigation/tenant-navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TenantNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call TenantNavigation() from the server but TenantNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation/tenant-navigation.tsx\",\n    \"TenantNavigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/app/%5BtenantSlug%5D/layout.tsx"], "sourcesContent": ["// app/[tenantSlug]/layout.tsx\r\nimport { redirect } from \"next/navigation\";\r\nimport { requireAuthentication } from \"@/lib/auth/server\";\r\nimport { TenantProvider } from \"@/components/providers/tenant-provider\";\r\nimport { TenantNavigation } from \"@/components/navigation/tenant-navigation\";\r\n\r\nexport default async function TenantLayout({\r\n  children,\r\n  params,\r\n}: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ tenantSlug: string }>;\r\n}) {\r\n  const { tenantSlug } = await params;\r\n\r\n  try {\r\n    // Server-side authentication and tenant validation\r\n    const session = await requireAuthentication(tenantSlug);\r\n\r\n    return (\r\n      <TenantProvider session={session} tenantSlug={tenantSlug}>\r\n        <div className='min-h-screen bg-gray-50'>\r\n          <TenantNavigation />\r\n          {children}\r\n        </div>\r\n      </TenantProvider>\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Tenant authentication failed:\", error);\r\n    redirect(`/login?redirect=/${tenantSlug}`);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;AAC9B;AAAA;AACA;AACA;AACA;;;;;;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;IAE7B,IAAI;QACF,mDAAmD;QACnD,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;QAE5C,qBACE,6WAAC,qJAAA,CAAA,iBAAc;YAAC,SAAS;YAAS,YAAY;sBAC5C,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,wJAAA,CAAA,mBAAgB;;;;;oBAChB;;;;;;;;;;;;IAIT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,EAAE,YAAY;IAC3C;AACF", "debugId": null}}]}