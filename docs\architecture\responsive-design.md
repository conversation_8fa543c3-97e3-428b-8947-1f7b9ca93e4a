# Responsive Design Architecture

## Overview

The SEO Dashboard implements a mobile-first responsive design strategy using Tailwind CSS breakpoints and modern CSS techniques. The system ensures optimal user experience across all device sizes while maintaining functionality and visual hierarchy.

## Breakpoint Strategy

### Tailwind CSS Breakpoints
```css
/* Mobile First Approach */
/* Default: 0px - 639px (Mobile) */
.mobile-default { }

/* sm: 640px and up (Large Mobile/Small Tablet) */
@media (min-width: 640px) { }

/* md: 768px and up (Tablet/Small Desktop) */
@media (min-width: 768px) { }

/* lg: 1024px and up (Desktop) */
@media (min-width: 1024px) { }

/* xl: 1280px and up (Large Desktop) */
@media (min-width: 1280px) { }
```

### Layout Breakpoints
- **Mobile**: < 768px - Single column, hamburger menu
- **Tablet**: 768px - 1023px - Sidebar appears, compact layout
- **Desktop**: 1024px+ - Full layout with expanded spacing

## Component Responsive Patterns

### Header Navigation
```tsx
// Responsive user name display
<span className='text-sm font-medium text-gray-700 hidden sm:block'>
  {session.user.firstName}
</span>

// Responsive button sizing
<Button variant='ghost' className='h-9 px-2 py-1.5'>
  <div className='flex items-center space-x-2'>
    {/* Avatar always visible */}
    <div className='h-6 w-6 rounded-full bg-gradient-to-br from-gray-400 to-gray-600'>
      <User className='h-3 w-3 text-white' />
    </div>
    {/* Name hidden on small screens */}
    <span className='text-sm font-medium text-gray-700 hidden sm:block'>
      {session.user.firstName}
    </span>
    <ChevronDown className='h-3 w-3 text-gray-500' />
  </div>
</Button>
```

### Sidebar Navigation
```tsx
// Desktop sidebar - fixed positioning
<div className='hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:top-16'>
  {/* Sidebar content */}
</div>

// Mobile sidebar - sheet overlay
<Sheet>
  <SheetContent side='left' className='w-64'>
    {/* Mobile menu content */}
  </SheetContent>
</Sheet>

// Mobile menu trigger
<div className='md:hidden fixed top-4 left-4 z-20'>
  <SheetTrigger asChild>
    <Button variant='ghost' size='sm'>
      <Menu className='h-6 w-6' />
    </Button>
  </SheetTrigger>
</div>
```

### Main Content Area
```tsx
// Responsive padding and margins
<div className='md:pl-64 pt-16'>
  <main className='flex-1'>
    <div className='py-6 px-4 sm:px-6 lg:px-8'>
      {children}
    </div>
  </main>
</div>
```

## Content Responsive Strategies

### Dashboard Cards
```tsx
// Responsive grid layout
<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6'>
  {/* Cards adapt to available space */}
</div>

// Responsive card padding
<div className='bg-white rounded-lg shadow p-4 sm:p-6'>
  {/* Card content */}
</div>
```

### Typography Scaling
```tsx
// Responsive heading sizes
<h1 className='text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900'>
  Dashboard Title
</h1>

// Responsive body text
<p className='text-sm sm:text-base text-gray-600'>
  Description text
</p>
```

### Data Tables
```tsx
// Mobile-first table approach
<div className='overflow-x-auto'>
  <table className='min-w-full divide-y divide-gray-200'>
    {/* Table content */}
  </table>
</div>

// Responsive table cells
<td className='px-2 sm:px-4 lg:px-6 py-4 whitespace-nowrap text-sm'>
  {/* Cell content */}
</td>
```

## Mobile-Specific Optimizations

### Touch Targets
```css
/* Minimum 44px touch targets */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Button spacing for touch */
.mobile-button-group {
  gap: 8px; /* Minimum 8px between touch targets */
}
```

### Mobile Navigation
```tsx
// Mobile-optimized menu
<SheetContent side='left' className='w-64 p-0'>
  <div className='flex flex-col h-full'>
    {/* Header */}
    <div className='flex items-center h-16 px-4 border-b'>
      <h2 className='text-lg font-semibold'>Navigation</h2>
    </div>
    
    {/* Navigation items with larger touch targets */}
    <nav className='flex-1 px-2 py-4 space-y-1'>
      {navigationItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className='flex items-center px-3 py-3 text-sm font-medium rounded-md hover:bg-gray-100'
        >
          <item.icon className='mr-3 h-5 w-5' />
          {item.name}
        </Link>
      ))}
    </nav>
  </div>
</SheetContent>
```

### Mobile Content Optimization
```tsx
// Stack elements vertically on mobile
<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
  <div className='mb-4 sm:mb-0'>
    <h1 className='text-2xl font-bold'>Page Title</h1>
  </div>
  <div className='flex flex-col sm:flex-row gap-2'>
    {/* Action buttons */}
  </div>
</div>
```

## Performance Considerations

### CSS Optimization
```css
/* Avoid expensive properties on mobile */
@media (max-width: 767px) {
  .avoid-on-mobile {
    /* Disable backdrop-filter on mobile for performance */
    backdrop-filter: none;
    /* Reduce box-shadows */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}
```

### Image Optimization
```tsx
// Responsive images with Next.js
<Image
  src={imageSrc}
  alt={imageAlt}
  width={400}
  height={300}
  sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  className='w-full h-auto'
/>
```

### Lazy Loading
```tsx
// Lazy load non-critical components on mobile
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <div className='animate-pulse bg-gray-200 h-32 rounded' />,
  ssr: false
});
```

## Accessibility Across Devices

### Focus Management
```tsx
// Ensure focus is visible on all devices
<button className='focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'>
  Button Text
</button>
```

### Screen Reader Support
```tsx
// Responsive screen reader text
<span className='sr-only sm:not-sr-only'>
  Full text on desktop, hidden on mobile
</span>
<span className='sm:sr-only'>
  Mobile-only text
</span>
```

## Testing Strategy

### Device Testing Matrix
| Device Type | Screen Size | Test Focus |
|-------------|-------------|------------|
| Mobile Phone | 375px - 414px | Touch interactions, menu usability |
| Large Mobile | 414px - 640px | Layout transitions, readability |
| Tablet | 768px - 1024px | Sidebar behavior, content flow |
| Desktop | 1024px+ | Full feature set, performance |

### Responsive Testing Tools
1. **Browser DevTools**: Chrome/Firefox responsive mode
2. **Real Devices**: iOS/Android testing
3. **Automated Testing**: Playwright with viewport testing
4. **Visual Regression**: Percy or similar tools

### Test Scenarios
```javascript
// Example responsive test
describe('Responsive Layout', () => {
  test('mobile navigation works correctly', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard');
    
    // Sidebar should be hidden
    await expect(page.locator('[data-testid="desktop-sidebar"]')).toBeHidden();
    
    // Mobile menu button should be visible
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    
    // Click mobile menu
    await page.click('[data-testid="mobile-menu-button"]');
    
    // Mobile menu should open
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
  });
});
```

## Common Responsive Patterns

### Container Queries (Future)
```css
/* When container queries are widely supported */
@container (min-width: 400px) {
  .card {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}
```

### Responsive Utilities
```tsx
// Custom responsive hooks
const useResponsive = () => {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkDevice();
    window.addEventListener('resize', checkDevice);
    
    return () => window.removeEventListener('resize', checkDevice);
  }, []);
  
  return { isMobile };
};
```

### Responsive Component Variants
```tsx
// Component that adapts to screen size
const ResponsiveCard = ({ children, ...props }) => {
  return (
    <div className={cn(
      'bg-white rounded-lg shadow',
      'p-4 sm:p-6 lg:p-8', // Responsive padding
      'mb-4 sm:mb-6 lg:mb-8', // Responsive margins
      props.className
    )}>
      {children}
    </div>
  );
};
```

## Best Practices

### Mobile-First Development
1. **Start with mobile design** - Design for smallest screen first
2. **Progressive enhancement** - Add features for larger screens
3. **Touch-friendly interfaces** - Minimum 44px touch targets
4. **Performance priority** - Optimize for mobile networks

### Content Strategy
1. **Prioritize content** - Most important content first
2. **Readable typography** - Appropriate font sizes for each device
3. **Scannable layouts** - Easy to digest on small screens
4. **Contextual actions** - Show relevant actions for each screen size

### Technical Implementation
1. **CSS-first approach** - Use CSS for responsive behavior when possible
2. **Avoid JavaScript for layout** - Rely on CSS Grid and Flexbox
3. **Test early and often** - Regular testing across devices
4. **Performance monitoring** - Track metrics across device types
