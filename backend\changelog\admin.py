"""
Changelog Admin Interface - Business-friendly content management.

Provides templates and helpers for creating changelog entries that follow
the verbiage guidelines for small business communication.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import ChangelogEntry, ChangelogTag, ChangelogMedia, ChangelogFeedback


class ChangelogMediaInline(admin.TabularInline):
    """Inline media management for changelog entries."""
    model = ChangelogMedia
    extra = 1
    fields = ['media_type', 'file', 'alt_text', 'caption', 'order']


class ChangelogFeedbackInline(admin.TabularInline):
    """Inline feedback display (read-only)."""
    model = ChangelogFeedback
    extra = 0
    readonly_fields = ['client', 'user', 'feedback_type', 'comment', 'created_at']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(ChangelogTag)
class ChangelogTagAdmin(admin.ModelAdmin):
    """Admin for changelog tags with color preview."""

    list_display = ['name', 'color_preview', 'description', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']

    def color_preview(self, obj):
        """Display color as a visual swatch."""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc; display: inline-block;"></div>',
            obj.color
        )
    color_preview.short_description = 'Color'


@admin.register(ChangelogEntry)
class ChangelogEntryAdmin(admin.ModelAdmin):
    """
    Admin for changelog entries with business-friendly helpers and templates.
    """

    list_display = [
        'title_with_emoji', 'entry_type', 'impact_level', 'client',
        'is_published', 'is_featured', 'published_date', 'author'
    ]

    list_filter = [
        'entry_type', 'impact_level', 'is_published', 'is_featured',
        'published_date', 'client', 'tags'
    ]

    search_fields = ['title', 'business_benefit', 'action_required']

    readonly_fields = ['created_at', 'updated_at']

    filter_horizontal = ['tags']

    inlines = [ChangelogMediaInline, ChangelogFeedbackInline]

    fieldsets = [
        ('📝 What Changed', {
            'fields': ['title', 'entry_type', 'impact_level'],
            'description': 'Clear, actionable title that immediately shows value. Use business language, not technical jargon.'
        }),
        ('💡 Why You\'ll Love It', {
            'fields': ['business_benefit'],
            'description': 'Focus on business outcomes and benefits. Answer: "How does this help me serve customers better or save time?"'
        }),
        ('🎯 What To Do', {
            'fields': ['action_required'],
            'description': 'Clear instructions for users. Can be "Nothing - it just works!" for automatic improvements.'
        }),
        ('🚀 Psychological Warfare Elements', {
            'fields': ['revenue_impact', 'social_proof', 'competitive_advantage'],
            'description': 'Optional: Add specific benefits, social validation, and competitive positioning.',
            'classes': ['collapse']
        }),
        ('📊 Publishing & Organization', {
            'fields': ['client', 'is_published', 'is_featured', 'published_date', 'tags'],
            'description': 'Leave client blank for platform-wide updates. Published date auto-sets when publishing.'
        }),
        ('👤 Metadata', {
            'fields': ['author', 'created_at', 'updated_at'],
            'classes': ['collapse']
        })
    ]

    def title_with_emoji(self, obj):
        """Display title with appropriate emoji."""
        return str(obj)
    title_with_emoji.short_description = 'Title'

    def save_model(self, request, obj, form, change):
        """Auto-set author if not specified."""
        if not change:  # New object
            obj.author = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        """Optimize queries with select_related."""
        return super().get_queryset(request).select_related('client', 'author').prefetch_related('tags')


@admin.register(ChangelogMedia)
class ChangelogMediaAdmin(admin.ModelAdmin):
    """Admin for changelog media files."""

    list_display = ['entry', 'media_type', 'alt_text', 'order', 'created_at']
    list_filter = ['media_type', 'created_at']
    search_fields = ['entry__title', 'alt_text', 'caption']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('entry')


@admin.register(ChangelogFeedback)
class ChangelogFeedbackAdmin(admin.ModelAdmin):
    """Admin for viewing changelog feedback."""

    list_display = ['entry', 'client', 'user', 'feedback_type', 'created_at']
    list_filter = ['feedback_type', 'created_at', 'client']
    search_fields = ['entry__title', 'comment', 'client__name']
    readonly_fields = ['entry', 'client', 'user', 'feedback_type', 'comment', 'created_at']

    def has_add_permission(self, request):
        """Feedback is created by users, not admin."""
        return False

    def has_change_permission(self, request, obj=None):
        """Feedback should be read-only."""
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('entry', 'client', 'user')
