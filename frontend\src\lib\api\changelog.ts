/**
 * Changelog API Client - Business-friendly changelog system integration.
 * 
 * Following established tenant-aware patterns with proper error handling
 * and type safety for changelog operations.
 */

import { fetchWithTenant } from './tenant-client';

// Type definitions matching Django serializers
export interface ChangelogTag {
  id: number;
  name: string;
  color: string;
  description: string;
}

export interface ChangelogMedia {
  id: number;
  media_type: 'image' | 'gif' | 'video';
  file: string;
  alt_text: string;
  caption: string;
  order: number;
  created_at: string;
}

export interface ChangelogEntryList {
  id: number;
  title: string;
  business_benefit: string;
  action_required: string;
  entry_type: 'new' | 'improvement' | 'bugfix' | 'announcement';
  entry_type_display: string;
  impact_level: 'high' | 'medium' | 'low';
  impact_level_display: string;
  revenue_impact: string;
  social_proof: string;
  competitive_advantage: string;
  is_featured: boolean;
  published_date: string;
  author_name: string;
  tags: ChangelogTag[];
  media_count: number;
  feedback_summary: {
    total: number;
    helpful: number;
    not_helpful: number;
  };
}

export interface ChangelogEntryDetail extends ChangelogEntryList {
  media: ChangelogMedia[];
  created_at: string;
  updated_at: string;
}

export interface ChangelogFeedback {
  id: number;
  entry: number;
  feedback_type: 'helpful' | 'not_helpful' | 'suggestion';
  comment: string;
  user_name: string;
  created_at: string;
}

export interface ChangelogStats {
  total_entries: number;
  featured_entries: number;
  by_type: Record<string, { count: number; display_name: string }>;
  by_impact: Record<string, { count: number; display_name: string }>;
  recent_count: number;
}

export interface ChangelogFilters {
  entry_type?: string;
  impact_level?: string;
  tags?: string;
  featured_only?: boolean;
}

/**
 * Get paginated list of changelog entries with optional filtering.
 */
export async function getChangelogEntries(
  tenantSlug: string,
  filters: ChangelogFilters = {},
  page: number = 1
): Promise<{
  results: ChangelogEntryList[];
  count: number;
  next: string | null;
  previous: string | null;
}> {
  const params = new URLSearchParams();
  
  if (filters.entry_type) params.append('entry_type', filters.entry_type);
  if (filters.impact_level) params.append('impact_level', filters.impact_level);
  if (filters.tags) params.append('tags', filters.tags);
  if (filters.featured_only) params.append('featured_only', 'true');
  if (page > 1) params.append('page', page.toString());
  
  const queryString = params.toString();
  const endpoint = `/changelog/entries/${queryString ? `?${queryString}` : ''}`;
  
  return fetchWithTenant(tenantSlug, endpoint);
}

/**
 * Get detailed information for a specific changelog entry.
 */
export async function getChangelogEntry(
  tenantSlug: string,
  entryId: number
): Promise<ChangelogEntryDetail> {
  return fetchWithTenant(tenantSlug, `/changelog/entries/${entryId}/`);
}

/**
 * Get changelog statistics for the current tenant.
 */
export async function getChangelogStats(
  tenantSlug: string
): Promise<ChangelogStats> {
  return fetchWithTenant(tenantSlug, '/changelog/entries/stats/');
}

/**
 * Get all available changelog tags for filtering.
 */
export async function getChangelogTags(
  tenantSlug: string
): Promise<ChangelogTag[]> {
  const response = await fetchWithTenant<{ results: ChangelogTag[] }>(
    tenantSlug, 
    '/changelog/tags/'
  );
  return response.results;
}

/**
 * Submit feedback for a changelog entry.
 */
export async function submitChangelogFeedback(
  tenantSlug: string,
  entryId: number,
  feedback: {
    feedback_type: 'helpful' | 'not_helpful' | 'suggestion';
    comment?: string;
  }
): Promise<ChangelogFeedback> {
  return fetchWithTenant(tenantSlug, `/changelog/entries/${entryId}/feedback/`, {
    method: 'POST',
    body: JSON.stringify(feedback),
  });
}

/**
 * Helper function to get the emoji for an entry type.
 */
export function getEntryTypeEmoji(entryType: string): string {
  const emojiMap: Record<string, string> = {
    'new': '🆕',
    'improvement': '⚡',
    'bugfix': '🐛',
    'announcement': '📢',
  };
  return emojiMap[entryType] || '📝';
}

/**
 * Helper function to get the color class for impact level.
 */
export function getImpactLevelColor(impactLevel: string): string {
  const colorMap: Record<string, string> = {
    'high': 'bg-red-100 text-red-800 border-red-200',
    'medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'low': 'bg-green-100 text-green-800 border-green-200',
  };
  return colorMap[impactLevel] || 'bg-gray-100 text-gray-800 border-gray-200';
}

/**
 * Helper function to format published date in a user-friendly way.
 */
export function formatPublishedDate(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    return 'Today';
  } else if (diffInDays === 1) {
    return 'Yesterday';
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
