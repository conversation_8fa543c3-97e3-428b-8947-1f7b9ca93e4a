/**
 * Changelog Entry Detail - Detailed view of individual changelog entry.
 * 
 * Server component that displays full changelog entry information
 * with media, business benefits, and psychological warfare elements.
 */

import { ChangelogEntryDetail, getEntryTypeEmoji, getImpactLevelColor, formatPublishedDate } from "@/lib/api/changelog";
import Image from "next/image";
import Link from "next/link";

interface ChangelogEntryDetailProps {
  entry: ChangelogEntryDetail;
}

export async function ChangelogEntryDetail({ entry }: ChangelogEntryDetailProps) {
  return (
    <article className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-8 py-6 border-b border-gray-200">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="text-4xl">
              {getEntryTypeEmoji(entry.entry_type)}
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                {entry.title}
              </h1>
              <div className="mt-3 flex items-center space-x-6 text-sm text-gray-500">
                <span className="flex items-center space-x-2">
                  <span>📅</span>
                  <span>{formatPublishedDate(entry.published_date)}</span>
                </span>
                {entry.author_name && (
                  <span className="flex items-center space-x-2">
                    <span>👤</span>
                    <span>by {entry.author_name}</span>
                  </span>
                )}
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getImpactLevelColor(entry.impact_level)}`}>
                  {entry.impact_level_display} Impact
                </span>
              </div>
            </div>
          </div>
          
          {entry.is_featured && (
            <div className="flex-shrink-0">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 border border-purple-200">
                ⭐ Featured Update
              </span>
            </div>
          )}
        </div>
        
        {/* Tags */}
        {entry.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {entry.tags.map((tag) => (
              <span
                key={tag.id}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                style={{
                  backgroundColor: `${tag.color}20`,
                  color: tag.color,
                  borderColor: `${tag.color}40`,
                  borderWidth: '1px'
                }}
              >
                {tag.name}
              </span>
            ))}
          </div>
        )}
      </div>
      
      {/* Content */}
      <div className="px-8 py-6 space-y-8">
        {/* Why You'll Love It */}
        <section>
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <span>💡</span>
            <span>Why You'll Love It</span>
          </h2>
          <div className="prose prose-gray max-w-none">
            <p className="text-lg text-gray-700 leading-relaxed">
              {entry.business_benefit}
            </p>
          </div>
        </section>
        
        {/* Psychological warfare elements */}
        {(entry.revenue_impact || entry.social_proof || entry.competitive_advantage) && (
          <section className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <span>🚀</span>
              <span>Your Business Advantage</span>
            </h3>
            <div className="space-y-3">
              {entry.revenue_impact && (
                <div className="flex items-start space-x-3">
                  <span className="text-green-600 text-lg">💰</span>
                  <div>
                    <span className="font-medium text-green-800">Revenue Impact:</span>
                    <span className="ml-2 text-gray-700">{entry.revenue_impact}</span>
                  </div>
                </div>
              )}
              {entry.social_proof && (
                <div className="flex items-start space-x-3">
                  <span className="text-blue-600 text-lg">👥</span>
                  <div>
                    <span className="font-medium text-blue-800">Join Others:</span>
                    <span className="ml-2 text-gray-700">{entry.social_proof}</span>
                  </div>
                </div>
              )}
              {entry.competitive_advantage && (
                <div className="flex items-start space-x-3">
                  <span className="text-purple-600 text-lg">🎯</span>
                  <div>
                    <span className="font-medium text-purple-800">Competitive Edge:</span>
                    <span className="ml-2 text-gray-700">{entry.competitive_advantage}</span>
                  </div>
                </div>
              )}
            </div>
          </section>
        )}
        
        {/* What To Do */}
        <section>
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <span>🎯</span>
            <span>What To Do</span>
          </h2>
          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <p className="text-lg text-gray-700 leading-relaxed">
              {entry.action_required}
            </p>
          </div>
        </section>
        
        {/* Media */}
        {entry.media.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <span>📸</span>
              <span>See It In Action</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {entry.media.map((media) => (
                <div key={media.id} className="space-y-3">
                  {media.media_type === 'image' && (
                    <div className="relative aspect-video rounded-lg overflow-hidden border border-gray-200">
                      <Image
                        src={media.file}
                        alt={media.alt_text}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  {media.media_type === 'gif' && (
                    <div className="relative aspect-video rounded-lg overflow-hidden border border-gray-200">
                      <Image
                        src={media.file}
                        alt={media.alt_text}
                        fill
                        className="object-cover"
                        unoptimized // For GIFs
                      />
                    </div>
                  )}
                  {media.media_type === 'video' && (
                    <div className="aspect-video rounded-lg overflow-hidden border border-gray-200">
                      <video
                        src={media.file}
                        controls
                        className="w-full h-full object-cover"
                        aria-label={media.alt_text}
                      />
                    </div>
                  )}
                  {media.caption && (
                    <p className="text-sm text-gray-600 italic">
                      {media.caption}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
      
      {/* Footer */}
      <div className="px-8 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div>
            Last updated: {new Date(entry.updated_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
          <Link
            href={`/${entry.id.toString().split('/')[1]}/changelog`}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← Back to Changelog
          </Link>
        </div>
      </div>
    </article>
  );
}
