"""
Changelog URL Configuration - API routing for changelog system.

Following established tenant-aware routing patterns with proper
REST API structure.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import ChangelogEntryViewSet, ChangelogTagViewSet

# Create router for changelog API endpoints
router = DefaultRouter()
router.register(r'entries', ChangelogEntryViewSet, basename='changelog-entries')
router.register(r'tags', ChangelogTagViewSet, basename='changelog-tags')

app_name = 'changelog'

urlpatterns = [
    # Include all router URLs
    path('', include(router.urls)),
]
