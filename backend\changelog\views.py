"""
Changelog Views - API endpoints for business-friendly changelog system.

Following established tenant-aware patterns with proper isolation and
business-focused data presentation.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Count, Prefetch
from django.utils import timezone

from tenants.mixins import TenantViewSetMixin
from .models import ChangelogEntry, ChangelogTag, ChangelogFeedback
from .serializers import (
    ChangelogEntryListSerializer,
    ChangelogEntryDetailSerializer,
    ChangelogTagSerializer,
    ChangelogFeedbackSerializer,
    ChangelogFeedbackCreateSerializer
)


class ChangelogEntryViewSet(TenantViewSetMixin, viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for changelog entries with tenant isolation.

    Provides read-only access to published changelog entries for the current tenant.
    Supports filtering by entry type, impact level, and tags.
    """

    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Get published changelog entries for current tenant or global entries.
        Optimized with prefetch_related for performance.
        """
        base_queryset = ChangelogEntry.objects.filter(
            Q(is_published=True) &
            (Q(client=self.request.tenant) | Q(client__isnull=True))
        ).select_related('author').prefetch_related(
            'tags',
            Prefetch('media', queryset=self.get_media_queryset()),
            Prefetch('feedback', queryset=self.get_feedback_queryset())
        ).order_by('-is_featured', '-published_date')

        return base_queryset

    def get_media_queryset(self):
        """Optimized media queryset."""
        from .models import ChangelogMedia
        return ChangelogMedia.objects.order_by('order', 'created_at')

    def get_feedback_queryset(self):
        """Tenant-filtered feedback queryset."""
        return ChangelogFeedback.objects.filter(
            client=self.request.tenant
        ).select_related('user')

    def get_serializer_class(self):
        """Use different serializers for list vs detail views."""
        if self.action == 'list':
            return ChangelogEntryListSerializer
        return ChangelogEntryDetailSerializer

    def list(self, request, *args, **kwargs):
        """
        List changelog entries with optional filtering.

        Query parameters:
        - entry_type: Filter by entry type (new, improvement, bugfix, announcement)
        - impact_level: Filter by impact level (high, medium, low)
        - tags: Comma-separated tag names
        - featured_only: Show only featured entries (true/false)
        """
        queryset = self.get_queryset()

        # Apply filters
        entry_type = request.query_params.get('entry_type')
        if entry_type:
            queryset = queryset.filter(entry_type=entry_type)

        impact_level = request.query_params.get('impact_level')
        if impact_level:
            queryset = queryset.filter(impact_level=impact_level)

        tags = request.query_params.get('tags')
        if tags:
            tag_names = [tag.strip() for tag in tags.split(',')]
            queryset = queryset.filter(tags__name__in=tag_names).distinct()

        featured_only = request.query_params.get('featured_only')
        if featured_only and featured_only.lower() == 'true':
            queryset = queryset.filter(is_featured=True)

        # Paginate and serialize
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def feedback(self, request, pk=None):
        """
        Submit feedback for a changelog entry.

        Expected payload:
        {
            "feedback_type": "helpful|not_helpful|suggestion",
            "comment": "Optional detailed feedback"
        }
        """
        entry = self.get_object()

        # Check if user already provided feedback
        existing_feedback = ChangelogFeedback.objects.filter(
            entry=entry,
            client=request.tenant,
            user=request.user if request.user.is_authenticated else None
        ).first()

        if existing_feedback:
            return Response(
                {'error': 'You have already provided feedback for this entry.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = ChangelogFeedbackCreateSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            feedback = serializer.save(entry=entry)
            response_serializer = ChangelogFeedbackSerializer(feedback)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Get changelog statistics for the current tenant.

        Returns counts by entry type, impact level, and recent activity.
        """
        queryset = self.get_queryset()

        stats = {
            'total_entries': queryset.count(),
            'featured_entries': queryset.filter(is_featured=True).count(),
            'by_type': {},
            'by_impact': {},
            'recent_count': queryset.filter(
                published_date__gte=timezone.now() - timezone.timedelta(days=30)
            ).count()
        }

        # Count by entry type
        for entry_type, display_name in ChangelogEntry.ENTRY_TYPES:
            stats['by_type'][entry_type] = {
                'count': queryset.filter(entry_type=entry_type).count(),
                'display_name': display_name
            }

        # Count by impact level
        for impact_level, display_name in ChangelogEntry.IMPACT_LEVELS:
            stats['by_impact'][impact_level] = {
                'count': queryset.filter(impact_level=impact_level).count(),
                'display_name': display_name
            }

        return Response(stats)


class ChangelogTagViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for changelog tags.

    Provides read-only access to all available tags for filtering.
    """

    queryset = ChangelogTag.objects.all()
    serializer_class = ChangelogTagSerializer
    permission_classes = [permissions.IsAuthenticated]
