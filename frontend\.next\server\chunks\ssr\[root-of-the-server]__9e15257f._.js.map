{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/react-query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ReactQueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/react-query-provider.tsx <module evaluation>\",\n    \"ReactQueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,mFACA", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/react-query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ReactQueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/react-query-provider.tsx\",\n    \"ReactQueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Toaster } from \"@/components/ui/sonner\";\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { ReactQueryProvider } from \"@/components/providers/react-query-provider\";\n\nconst inter = Inter({ subsets: [\"latin\"] });\n\nexport const metadata: Metadata = {\n  title: \"SEO Dashboard\",\n  description: \"Professional SEO management and analytics platform\",\n  keywords: \"SEO, analytics, dashboard, keywords, performance, insights\",\n  authors: [{ name: \"SEO Dashboard Team\" }],\n  robots: \"noindex, nofollow\", // Prevent indexing of the dashboard\n};\n\nexport const viewport = {\n  width: \"device-width\",\n  initialScale: 1,\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang='en'>\n      <body className={inter.className}>\n        <ReactQueryProvider>\n          {children}\n          <Toaster />\n        </ReactQueryProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;;;;;;AAIO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAqB;KAAE;IACzC,QAAQ;AACV;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,6WAAC;QAAK,MAAK;kBACT,cAAA,6WAAC;YAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;sBAC9B,cAAA,6WAAC,6JAAA,CAAA,qBAAkB;;oBAChB;kCACD,6WAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;AAKlB", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/node_modules/.pnpm/next%4015.4.4_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}