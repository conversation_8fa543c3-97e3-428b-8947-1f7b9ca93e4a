# Generated by Django 5.1.4 on 2025-07-28 03:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('tenants', '0002_add_subscription_fields'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChangelogTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('color', models.CharField(default='#3B82F6', help_text='Hex color code for tag display', max_length=7)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ChangelogEntry',
            fields=[
                ('id', models.<PERSON>A<PERSON>Field(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text="What Changed - Clear, actionable title (e.g., 'Faster report loading')", max_length=200)),
                ('business_benefit', models.TextField(help_text="Why You'll Love It - Focus on business value, not technical details")),
                ('action_required', models.TextField(help_text="What To Do - Clear instructions for users (can be 'Nothing - it just works!')")),
                ('entry_type', models.CharField(choices=[('new', '🆕 New Feature'), ('improvement', '⚡ Improvement'), ('bugfix', '🐛 Bug Fix'), ('announcement', '📢 Announcement')], help_text='Type of update with emoji prefix', max_length=20)),
                ('impact_level', models.CharField(choices=[('high', 'High Impact'), ('medium', 'Medium Impact'), ('low', 'Low Impact')], help_text='Business impact level for prioritization', max_length=10)),
                ('revenue_impact', models.CharField(blank=True, help_text="Specific revenue benefit (e.g., 'Save 2 hours per week', '$500/month savings')", max_length=100)),
                ('social_proof', models.CharField(blank=True, help_text="Social validation (e.g., 'Join 500+ businesses already using this')", max_length=200)),
                ('competitive_advantage', models.TextField(blank=True, help_text='How this gives them an edge over competitors')),
                ('is_published', models.BooleanField(default=False, help_text='Whether this entry is visible to users')),
                ('is_featured', models.BooleanField(default=False, help_text='Highlight this entry (for major updates)')),
                ('published_date', models.DateTimeField(blank=True, help_text='When this was published (auto-set when is_published=True)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(help_text='Team member who created this entry', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('client', models.ForeignKey(blank=True, help_text='Tenant that owns this changelog entry', null=True, on_delete=django.db.models.deletion.CASCADE, to='tenants.client')),
                ('tags', models.ManyToManyField(blank=True, help_text='Categories for filtering and organization', to='changelog.changelogtag')),
            ],
            options={
                'verbose_name': 'Changelog Entry',
                'verbose_name_plural': 'Changelog Entries',
                'ordering': ['-published_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChangelogMedia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('media_type', models.CharField(choices=[('image', 'Screenshot/Image'), ('gif', 'Animated GIF'), ('video', 'Video')], max_length=10)),
                ('file', models.FileField(upload_to='changelog/media/')),
                ('alt_text', models.CharField(help_text='Accessibility description of the media', max_length=200)),
                ('caption', models.TextField(blank=True, help_text="Optional caption explaining what's shown")),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order for multiple media items')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media', to='changelog.changelogentry')),
            ],
            options={
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='ChangelogFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(choices=[('helpful', 'Helpful'), ('not_helpful', 'Not Helpful'), ('suggestion', 'Suggestion')], max_length=20)),
                ('comment', models.TextField(blank=True, help_text='Optional detailed feedback')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('client', models.ForeignKey(help_text='Tenant that provided this feedback', on_delete=django.db.models.deletion.CASCADE, to='tenants.client')),
                ('entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='changelog.changelogentry')),
                ('user', models.ForeignKey(blank=True, help_text='User who provided feedback (if logged in)', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('entry', 'client', 'user')},
            },
        ),
    ]
