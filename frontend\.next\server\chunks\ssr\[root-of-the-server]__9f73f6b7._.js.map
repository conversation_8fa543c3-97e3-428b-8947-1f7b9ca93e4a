{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Toaster as Sonner } from \"sonner\"\r\n\r\ntype ToasterProps = React.ComponentProps<typeof Sonner>\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  return (\r\n    <Sonner\r\n      theme=\"light\"\r\n      className=\"toaster group\"\r\n      toastOptions={{\r\n        classNames: {\r\n          toast:\r\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\r\n          description: \"group-[.toast]:text-muted-foreground\",\r\n          actionButton:\r\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\r\n          cancelButton:\r\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\r\n        },\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAMA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,qBACE,6WAAC,wQAAA,CAAA,UAAM;QACL,OAAM;QACN,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/react-query-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\r\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\r\nimport { useState } from 'react';\r\n\r\ninterface ReactQueryProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function ReactQueryProvider({ children }: ReactQueryProviderProps) {\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            // With SSR, we usually want to set some default staleTime\r\n            // above 0 to avoid refetching immediately on the client\r\n            staleTime: 60 * 1000, // 1 minute\r\n            retry: (failureCount, error: any) => {\r\n              // Don't retry on 401/403 errors\r\n              if (error?.response?.status === 401 || error?.response?.status === 403) {\r\n                return false;\r\n              }\r\n              // Retry up to 3 times for other errors\r\n              return failureCount < 3;\r\n            },\r\n          },\r\n          mutations: {\r\n            retry: false,\r\n          },\r\n        },\r\n      })\r\n  );\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n      <ReactQueryDevtools initialIsOpen={false} />\r\n    </QueryClientProvider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAUO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,sPAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,0DAA0D;oBAC1D,wDAAwD;oBACxD,WAAW,KAAK;oBAChB,OAAO,CAAC,cAAc;wBACpB,gCAAgC;wBAChC,IAAI,OAAO,UAAU,WAAW,OAAO,OAAO,UAAU,WAAW,KAAK;4BACtE,OAAO;wBACT;wBACA,uCAAuC;wBACvC,OAAO,eAAe;oBACxB;gBACF;gBACA,WAAW;oBACT,OAAO;gBACT;YACF;QACF;IAGJ,qBACE,6WAAC,sRAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,6WAAC,wRAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC", "debugId": null}}]}