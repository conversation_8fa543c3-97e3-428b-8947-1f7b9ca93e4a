{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/auth-config.ts"], "sourcesContent": ["// lib/auth-config.ts - Clean enterprise configuration\nexport const AUTH_CONFIG = {\n  API_BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',\n} as const;\n\n// Enterprise session configuration\nexport const SESSION_CONFIG = {\n  COOKIE_NAME: 'auth_token',\n  MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours in milliseconds\n  SECURE: process.env.NODE_ENV === 'production',\n  SAME_SITE: 'lax' as const,\n} as const;\n  \n  // lib/api-client.ts\n  interface AuthResponse {\n    token: string\n    user: {\n      id: number\n      email: string\n      firstName: string\n      lastName: string\n      role: string\n      createdAt: string\n      updatedAt: string\n    }\n    client: {\n      id: number\n      slug: string\n      name: string\n      websiteUrl: string\n      industry: string\n    }\n  }\n  \n  interface LoginData {\n    email: string\n    password: string\n  }\n  \n  interface RegisterData {\n    first_name: string\n    last_name: string\n    email: string\n    password: string\n    company_name: string\n    website_url?: string\n    industry?: string\n  }\n  \n  class ApiClient {\n    private baseUrl: string\n  \n    constructor(baseUrl: string) {\n      this.baseUrl = baseUrl\n    }\n  \n    private async request<T>(\n      endpoint: string,\n      options: RequestInit = {}\n    ): Promise<T> {\n      const url = `${this.baseUrl}${endpoint}`\n      \n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      }\n  \n      // Add auth token if available\n      if (typeof window !== 'undefined') {\n        const token = localStorage.getItem(AUTH_CONFIG.API_BASE_URL)\n        if (token) {\n          config.headers = {\n            ...config.headers,\n            Authorization: `Token ${token}`,\n          }\n        }\n      }\n  \n      const response = await fetch(url, config)\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)\n      }\n  \n      return response.json()\n    }\n  \n    async login(data: LoginData): Promise<AuthResponse> {\n      return this.request<AuthResponse>('/api/auth/login/', {\n        method: 'POST',\n        body: JSON.stringify(data),\n      })\n    }\n  \n    async register(data: RegisterData): Promise<AuthResponse> {\n      return this.request<AuthResponse>('/api/auth/register/', {\n        method: 'POST',\n        body: JSON.stringify(data),\n      })\n    }\n  \n    async logout(): Promise<void> {\n      await this.request('/api/auth/logout/', {\n        method: 'POST',\n      })\n    }\n  \n    async refreshToken(): Promise<{ token: string }> {\n      return this.request<{ token: string }>('/api/auth/refresh/', {\n        method: 'POST',\n      })\n    }\n  }\n  \n  export const apiClient = new ApiClient(AUTH_CONFIG.API_BASE_URL)\n  "], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AAC/C,MAAM,cAAc;IACzB,cAAc,6DAAmC;AACnD;AAGO,MAAM,iBAAiB;IAC5B,aAAa;IACb,SAAS,KAAK,KAAK,KAAK;IACxB,QAAQ,oDAAyB;IACjC,WAAW;AACb;AAsCE,MAAM;IACI,QAAe;IAEvB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,8BAA8B;QAC9B;;QAUA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC9E;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAM,IAAe,EAAyB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAe,oBAAoB;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAS,IAAkB,EAAyB;QACxD,OAAO,IAAI,CAAC,OAAO,CAAe,uBAAuB;YACvD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAwB;QAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB;YACtC,QAAQ;QACV;IACF;IAEA,MAAM,eAA2C;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAoB,sBAAsB;YAC3D,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI,UAAU,YAAY,YAAY"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/auth/server.ts"], "sourcesContent": ["// lib/auth/server.ts - Fixed export\nimport { cookies } from 'next/headers';\nimport { AUTH_CONFIG, SESSION_CONFIG } from '../auth-config';\n\nexport interface ServerSession {\n  user: {\n    id: number;\n    email: string;\n    firstName: string;\n    lastName: string;\n    role: string;\n  };\n  client: {\n    id: number;\n    slug: string;\n    name: string;\n    websiteUrl: string;\n    industry: string;\n  };\n  token: string;\n}\n\n/**\n * Get authenticated session from HTTP-only cookies\n * No localStorage dependencies\n */\nexport async function getServerSession(): Promise<ServerSession | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get(SESSION_CONFIG.COOKIE_NAME)?.value;\n\n    if (!token) {\n      return null;\n    }\n\n    return await validateTokenWithBackend(token);\n  } catch (error) {\n    console.error('Server session validation failed:', error);\n    return null;\n  }\n}\n\n/**\n * Validate token with Django backend\n * ✅ FIXED: Now exported for middleware use\n */\nexport async function validateTokenWithBackend(token: string): Promise<ServerSession | null> {\n  try {\n    const controller = new AbortController();\n    const timeout = setTimeout(() => controller.abort(), 10000);\n\n    const response = await fetch(`${AUTH_CONFIG.API_BASE_URL}/api/auth/validate/`, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Token ${token}`,\n        'Content-Type': 'application/json',\n      },\n      cache: 'no-store',\n      signal: controller.signal,\n    });\n\n    clearTimeout(timeout);\n\n    if (!response.ok) {\n      return null;\n    }\n\n    const data = await response.json();\n    \n    return {\n      user: data.user,\n      client: data.client,\n      token,\n    };\n  } catch (error) {\n    console.error('Token validation failed:', error);\n    return null;\n  }\n}\n\n/**\n * Validate tenant access for fortress-level security\n */\nexport async function validateTenantAccess(\n  session: ServerSession,\n  tenantSlug: string\n): Promise<boolean> {\n  return session.client.slug === tenantSlug;\n}\n\n/**\n * Require authentication with tenant validation\n */\nexport async function requireAuthentication(tenantSlug: string): Promise<ServerSession> {\n  const session = await getServerSession();\n  \n  if (!session) {\n    throw new Error('Authentication required');\n  }\n\n  const validTenant = await validateTenantAccess(session, tenantSlug);\n  if (!validTenant) {\n    throw new Error('Tenant access denied');\n  }\n\n  return session;\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;AACpC;AAAA;AACA;;;AAwBO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,oIAAA,CAAA,iBAAc,CAAC,WAAW,GAAG;QAE3D,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO,MAAM,yBAAyB;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAMO,eAAe,yBAAyB,KAAa;IAC1D,IAAI;QACF,MAAM,aAAa,IAAI;QACvB,MAAM,UAAU,WAAW,IAAM,WAAW,KAAK,IAAI;QAErD,MAAM,WAAW,MAAM,MAAM,GAAG,oIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;YAC7E,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,MAAM,EAAE,OAAO;gBACjC,gBAAgB;YAClB;YACA,OAAO;YACP,QAAQ,WAAW,MAAM;QAC3B;QAEA,aAAa;QAEb,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;YACnB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAKO,eAAe,qBACpB,OAAsB,EACtB,UAAkB;IAElB,OAAO,QAAQ,MAAM,CAAC,IAAI,KAAK;AACjC;AAKO,eAAe,sBAAsB,UAAkB;IAC5D,MAAM,UAAU,MAAM;IAEtB,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,cAAc,MAAM,qBAAqB,SAAS;IACxD,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT"}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["// middleware.ts - Enhanced and constitutional compliant\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport { validateTokenWithBackend } from '@/lib/auth/server';\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n  \r\n  // Skip middleware for public paths\r\n  const publicPaths = [\r\n    '/login',\r\n    '/register', \r\n    '/api',\r\n    '/_next',\r\n    '/favicon.ico',\r\n    '/unauthorized',\r\n    '/', // Add root path\r\n  ];\r\n  \r\n  if (publicPaths.some(path => pathname.startsWith(path))) {\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Only protect tenant routes (format: /[tenantSlug]/*)\r\n  const tenantRouteMatch = pathname.match(/^\\/([^\\/]+)\\/(.+)/);\r\n  if (!tenantRouteMatch) {\r\n    return NextResponse.next();\r\n  }\r\n\r\n  const [, tenantSlug] = tenantRouteMatch;\r\n  \r\n  // Simple authentication check\r\n  const authToken = request.cookies.get('auth_token')?.value;\r\n  if (!authToken) {\r\n    console.warn('[Middleware] No auth token found, redirecting to login', { \r\n      path: pathname, \r\n      tenantSlug \r\n    });\r\n    return NextResponse.redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));\r\n  }\r\n\r\n  try {\r\n    // Validate session with backend\r\n    const session = await validateTokenWithBackend(authToken);\r\n    \r\n    if (!session) {\r\n      console.warn('[Middleware] Invalid session, redirecting to login', { \r\n        path: pathname, \r\n        tenantSlug \r\n      });\r\n      return NextResponse.redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));\r\n    }\r\n    \r\n    if (session.client.slug !== tenantSlug) {\r\n      console.error('[Middleware] Tenant mismatch, redirecting to login', { \r\n        path: pathname, \r\n        expected: tenantSlug,\r\n        actual: session.client.slug,\r\n        user: session.user.email\r\n      });\r\n      return NextResponse.redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));\r\n    }\r\n\r\n    // Success - allow access\r\n    return NextResponse.next();\r\n\r\n  } catch (error) {\r\n    console.error('[Middleware] Authentication error', { \r\n      path: pathname, \r\n      tenantSlug,\r\n      error: error instanceof Error ? error.message : String(error)\r\n    });\r\n    return NextResponse.redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));\r\n  }\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    '/((?!api|_next/static|_next/image|favicon.ico|login|register).*)',\r\n  ],\r\n};\r\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;;AACxD;AAAA;AACA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,mCAAmC;IACnC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,QAAQ;QACvD,OAAO,4TAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,uDAAuD;IACvD,MAAM,mBAAmB,SAAS,KAAK,CAAC;IACxC,IAAI,CAAC,kBAAkB;QACrB,OAAO,4TAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,MAAM,GAAG,WAAW,GAAG;IAEvB,8BAA8B;IAC9B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACrD,IAAI,CAAC,WAAW;QACd,QAAQ,IAAI,CAAC,0DAA0D;YACrE,MAAM;YACN;QACF;QACA,OAAO,4TAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,WAAW,gBAAgB,EAAE,UAAU,EAAE,QAAQ,GAAG;IAC/F;IAEA,IAAI;QACF,gCAAgC;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,oIAAA,CAAA,2BAAwB,AAAD,EAAE;QAE/C,IAAI,CAAC,SAAS;YACZ,QAAQ,IAAI,CAAC,sDAAsD;gBACjE,MAAM;gBACN;YACF;YACA,OAAO,4TAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,WAAW,gBAAgB,EAAE,UAAU,EAAE,QAAQ,GAAG;QAC/F;QAEA,IAAI,QAAQ,MAAM,CAAC,IAAI,KAAK,YAAY;YACtC,QAAQ,KAAK,CAAC,sDAAsD;gBAClE,MAAM;gBACN,UAAU;gBACV,QAAQ,QAAQ,MAAM,CAAC,IAAI;gBAC3B,MAAM,QAAQ,IAAI,CAAC,KAAK;YAC1B;YACA,OAAO,4TAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,WAAW,gBAAgB,EAAE,UAAU,EAAE,QAAQ,GAAG;QAC/F;QAEA,yBAAyB;QACzB,OAAO,4TAAA,CAAA,eAAY,CAAC,IAAI;IAE1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;YACjD,MAAM;YACN;YACA,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;QACA,OAAO,4TAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,WAAW,gBAAgB,EAAE,UAAU,EAAE,QAAQ,GAAG;IAC/F;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}