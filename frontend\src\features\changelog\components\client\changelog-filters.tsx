'use client';

/**
 * Changelog Filters - Interactive filtering for changelog entries.
 * 
 * Client component that provides filtering options for entry type,
 * impact level, tags, and featured status.
 */

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getChangelogTags, ChangelogTag } from '@/lib/api/changelog';

interface ChangelogFiltersProps {
  tenantSlug: string;
  currentFilters: {
    entry_type?: string;
    impact_level?: string;
    tags?: string;
    featured_only?: string;
  };
}

const ENTRY_TYPES = [
  { value: 'new', label: '🆕 New Features', color: 'bg-green-100 text-green-800' },
  { value: 'improvement', label: '⚡ Improvements', color: 'bg-blue-100 text-blue-800' },
  { value: 'bugfix', label: '🐛 Bug Fixes', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'announcement', label: '📢 Announcements', color: 'bg-purple-100 text-purple-800' },
];

const IMPACT_LEVELS = [
  { value: 'high', label: 'High Impact', color: 'bg-red-100 text-red-800' },
  { value: 'medium', label: 'Medium Impact', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'low', label: 'Low Impact', color: 'bg-green-100 text-green-800' },
];

export function ChangelogFilters({ tenantSlug, currentFilters }: ChangelogFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [tags, setTags] = useState<ChangelogTag[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadTags() {
      try {
        const tagData = await getChangelogTags(tenantSlug);
        setTags(tagData);
      } catch (error) {
        console.error('Failed to load tags:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadTags();
  }, [tenantSlug]);

  const updateFilter = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams);
    
    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    
    // Reset to first page when filtering
    params.delete('page');
    
    const queryString = params.toString();
    const newUrl = `/${tenantSlug}/changelog${queryString ? `?${queryString}` : ''}`;
    
    router.push(newUrl);
  };

  const clearAllFilters = () => {
    router.push(`/${tenantSlug}/changelog`);
  };

  const hasActiveFilters = Object.values(currentFilters).some(value => value);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Filter Updates</h2>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Clear all filters
          </button>
        )}
      </div>
      
      <div className="space-y-6">
        {/* Entry Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Update Type
          </label>
          <div className="flex flex-wrap gap-2">
            {ENTRY_TYPES.map((type) => (
              <button
                key={type.value}
                onClick={() => updateFilter('entry_type', 
                  currentFilters.entry_type === type.value ? null : type.value
                )}
                className={`px-3 py-2 rounded-full text-sm font-medium border transition-colors ${
                  currentFilters.entry_type === type.value
                    ? `${type.color} border-current`
                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                }`}
              >
                {type.label}
              </button>
            ))}
          </div>
        </div>

        {/* Impact Level Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Business Impact
          </label>
          <div className="flex flex-wrap gap-2">
            {IMPACT_LEVELS.map((level) => (
              <button
                key={level.value}
                onClick={() => updateFilter('impact_level', 
                  currentFilters.impact_level === level.value ? null : level.value
                )}
                className={`px-3 py-2 rounded-full text-sm font-medium border transition-colors ${
                  currentFilters.impact_level === level.value
                    ? `${level.color} border-current`
                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                }`}
              >
                {level.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tags Filter */}
        {!isLoading && tags.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Categories
            </label>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => {
                const isActive = currentFilters.tags?.split(',').includes(tag.name);
                return (
                  <button
                    key={tag.id}
                    onClick={() => {
                      const currentTags = currentFilters.tags?.split(',').filter(Boolean) || [];
                      let newTags;
                      
                      if (isActive) {
                        newTags = currentTags.filter(t => t !== tag.name);
                      } else {
                        newTags = [...currentTags, tag.name];
                      }
                      
                      updateFilter('tags', newTags.length > 0 ? newTags.join(',') : null);
                    }}
                    className={`px-3 py-2 rounded-full text-sm font-medium border transition-colors ${
                      isActive
                        ? 'border-current'
                        : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                    }`}
                    style={isActive ? {
                      backgroundColor: `${tag.color}20`,
                      color: tag.color,
                      borderColor: `${tag.color}60`
                    } : {}}
                  >
                    {tag.name}
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Featured Only Toggle */}
        <div>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={currentFilters.featured_only === 'true'}
              onChange={(e) => updateFilter('featured_only', e.target.checked ? 'true' : null)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">
              Show only featured updates ⭐
            </span>
          </label>
        </div>
      </div>
    </div>
  );
}
