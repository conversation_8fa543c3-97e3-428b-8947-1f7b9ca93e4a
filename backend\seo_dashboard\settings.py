"""
Django settings for seo_dashboard project.
"""

from pathlib import Path

# === Added for environment variable and database support ===
import os
from decouple import config
from urllib.parse import urlparse, parse_qsl


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('DJANGO_SECRET_KEY', default='django-insecure-87odpes@zd85xlpxaz11@dv!q58krp_f(c9855x5plo0iavk9j')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DJANGO_DEBUG', default=True, cast=bool)

ALLOWED_HOSTS = config('DJANGO_ALLOWED_HOSTS', default='localhost,127.0.0.1', cast=lambda v: [s.strip() for s in v.split(',') if s.strip()])

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # Third party
    'rest_framework',
    'rest_framework.authtoken',
    'corsheaders',
    'channels',
    
    # Local apps
    'tenants',
    'core',
    'seo_data',
    'ai_insights',
    'integrations',
    'changelog',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # Move CORS early in the stack
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'tenants.middleware.CorrelationIdMiddleware',
    'tenants.middleware.TenantMiddleware',  # Add tenant extraction middleware
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'seo_dashboard.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'seo_dashboard.wsgi.application'
ASGI_APPLICATION = 'seo_dashboard.asgi.application'

# Database - Neon.tech Configuration
import dj_database_url

DATABASES = {
    'default': dj_database_url.parse(config('DATABASE_URL'))
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django REST Framework Settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'tenants.authentication.TenantAwareAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 50,
}

# CORS settings
# ---
# CORS and Cookie Settings for SSR Authentication with Next.js
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = False  # Set to False for security

# Allow all standard methods for API operations
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

from corsheaders.defaults import default_headers
CORS_ALLOW_HEADERS = list(default_headers) + [
    'x-tenant-slug',
    'x-correlation-id',
]

# Preflight cache duration (in seconds)
CORS_PREFLIGHT_MAX_AGE = 86400

SESSION_COOKIE_SAMESITE = 'Lax'  # Less strict than 'Strict' for development
CSRF_COOKIE_SAMESITE = 'Lax'
# In production, set SESSION_COOKIE_SECURE = True and CSRF_COOKIE_SECURE = True

# Celery Configuration (using Upstash)
CELERY_BROKER_URL = config('UPSTASH_REDIS_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = config('UPSTASH_REDIS_URL', default='redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'

# AI Configuration
OPENAI_API_KEY = config('OPENAI_API_KEY', default='')

# AI Configuration
AI_PRIMARY_PROVIDER = config('AI_PRIMARY_PROVIDER', default='fireworks')
AI_PRIMARY_MODEL = config('AI_PRIMARY_MODEL', default='deepseek-r1-basic')
AI_FALLBACK_PROVIDER = config('AI_FALLBACK_PROVIDER', default='openai')
AI_FALLBACK_MODEL = config('AI_FALLBACK_MODEL', default='gpt-4o-mini')

# Fireworks AI Settings
FIREWORKS_API_KEY = config('FIREWORKS_API_KEY')
FIREWORKS_SDK_DEBUG = config('FIREWORKS_SDK_DEBUG', default=False, cast=bool)

# AI Cost Controls
AI_MONTHLY_BUDGET = 200  # dollars
AI_USAGE_ALERTS = [100, 150, 180]  # Alert thresholds

# Channels Configuration for Real-Time WebSocket Updates
# Uses Upstash Redis for production scaling
UPSTASH_REDIS_REST_URL = os.environ.get('UPSTASH_REDIS_REST_URL')
UPSTASH_REDIS_REST_TOKEN = os.environ.get('UPSTASH_REDIS_REST_TOKEN')

if UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN:
    # Production: Use Upstash Redis
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                'hosts': [f"redis://default:{UPSTASH_REDIS_REST_TOKEN}@{UPSTASH_REDIS_REST_URL.replace('https://', '').replace('http://', '')}:6379"],
            },
        },
    }
else:
    # Development: Use local Redis
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                'hosts': [('127.0.0.1', 6379)],
            },
        },
    }

# Cloudflare R2 Storage Configuration
# S3-compatible object storage with no egress charges
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
STATICFILES_STORAGE = 'storages.backends.s3boto3.S3StaticStorage'

AWS_ACCESS_KEY_ID = os.environ.get('R2_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('R2_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = os.environ.get('R2_BUCKET_NAME', 'seo-intelligence-data')
AWS_S3_ENDPOINT_URL = os.environ.get('R2_ENDPOINT_URL')
AWS_S3_REGION_NAME = 'auto'
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = None
AWS_S3_VERIFY = True

# Google API Configuration
GOOGLE_OAUTH2_CLIENT_ID = os.environ.get('GOOGLE_OAUTH2_CLIENT_ID')
GOOGLE_OAUTH2_CLIENT_SECRET = os.environ.get('GOOGLE_OAUTH2_CLIENT_SECRET')
GOOGLE_SEARCH_CONSOLE_CREDENTIALS = os.environ.get('GOOGLE_SEARCH_CONSOLE_CREDENTIALS')
GOOGLE_ANALYTICS_CREDENTIALS = os.environ.get('GOOGLE_ANALYTICS_CREDENTIALS')
GOOGLE_BUSINESS_PROFILE_CREDENTIALS = os.environ.get('GOOGLE_BUSINESS_PROFILE_CREDENTIALS')
GOOGLE_PAGESPEED_API_KEY = os.environ.get('GOOGLE_PAGESPEED_API_KEY')

# Web Scraping Configuration
CRAWL4AI_USER_AGENT = os.environ.get('CRAWL4AI_USER_AGENT', 'SEO-Intelligence-Bot/1.0')
CRAWL4AI_RESPECT_ROBOTS_TXT = os.environ.get('CRAWL4AI_RESPECT_ROBOTS_TXT', 'True').lower() == 'true'
CRAWL4AI_DELAY_BETWEEN_REQUESTS = int(os.environ.get('CRAWL4AI_DELAY_BETWEEN_REQUESTS', '2'))

# Celery Configuration for Background Jobs
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379'))
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', os.environ.get('REDIS_URL', 'redis://127.0.0.1:6379'))
CELERY_TASK_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'
