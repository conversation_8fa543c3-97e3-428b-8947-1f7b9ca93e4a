(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_53558c81._.js",
  "static/chunks/0e117_@tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_97d0640f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_3b2d4161._.js",
  "static/chunks/0e117_@tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_97d0640f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript)");
    });
});
}),
}]);