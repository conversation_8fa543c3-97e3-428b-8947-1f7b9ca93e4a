{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_edf14df5._.js", "server/edge/chunks/[root-of-the-server]__36aff2c4._.js", "server/edge/chunks/edge-wrapper_d6e9ab4b.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|login|register).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|login|register).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "sY9VHniq8FSNDaV8sSNa3Soqsqz43QYIZGN1lU3MHWk=", "__NEXT_PREVIEW_MODE_ID": "3a05dd0ef218af48bcabb5c713726854", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d3a30996936e760f5b5b1394a8d6757954a7c8dd0903ba15e37f8b4771ef02f2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d4b1ded0abf4149020ab30ddfc125402e267d9d7a0bdb850f910d31a03f52a8c"}}}, "instrumentation": null, "functions": {}}