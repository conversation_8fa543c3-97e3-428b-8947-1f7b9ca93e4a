"""
Changelog Serializers - API serialization for business-friendly changelog system.

Following established DRF patterns with proper tenant isolation and
business-focused field organization.
"""

from rest_framework import serializers
from .models import ChangelogEntry, ChangelogTag, ChangelogMedia, ChangelogFeedback


class ChangelogTagSerializer(serializers.ModelSerializer):
    """Serializer for changelog tags."""
    
    class Meta:
        model = ChangelogTag
        fields = ['id', 'name', 'color', 'description']


class ChangelogMediaSerializer(serializers.ModelSerializer):
    """Serializer for changelog media files."""
    
    class Meta:
        model = ChangelogMedia
        fields = [
            'id', 'media_type', 'file', 'alt_text', 
            'caption', 'order', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ChangelogEntryListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for changelog entry lists.
    Optimized for the main changelog page display.
    """
    
    entry_type_display = serializers.CharField(source='get_entry_type_display', read_only=True)
    impact_level_display = serializers.CharField(source='get_impact_level_display', read_only=True)
    author_name = serializers.CharField(source='author.get_full_name', read_only=True)
    tags = ChangelogTagSerializer(many=True, read_only=True)
    media_count = serializers.SerializerMethodField()
    feedback_summary = serializers.SerializerMethodField()
    
    class Meta:
        model = ChangelogEntry
        fields = [
            'id', 'title', 'business_benefit', 'action_required',
            'entry_type', 'entry_type_display', 'impact_level', 'impact_level_display',
            'revenue_impact', 'social_proof', 'competitive_advantage',
            'is_featured', 'published_date', 'author_name', 'tags',
            'media_count', 'feedback_summary'
        ]
    
    def get_media_count(self, obj):
        """Count of media items for this entry."""
        return obj.media.count()
    
    def get_feedback_summary(self, obj):
        """Summary of feedback for this entry."""
        feedback = obj.feedback.all()
        return {
            'total': feedback.count(),
            'helpful': feedback.filter(feedback_type='helpful').count(),
            'not_helpful': feedback.filter(feedback_type='not_helpful').count(),
        }


class ChangelogEntryDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for individual changelog entries.
    Includes all related data for the detail view.
    """
    
    entry_type_display = serializers.CharField(source='get_entry_type_display', read_only=True)
    impact_level_display = serializers.CharField(source='get_impact_level_display', read_only=True)
    author_name = serializers.CharField(source='author.get_full_name', read_only=True)
    tags = ChangelogTagSerializer(many=True, read_only=True)
    media = ChangelogMediaSerializer(many=True, read_only=True)
    
    class Meta:
        model = ChangelogEntry
        fields = [
            'id', 'title', 'business_benefit', 'action_required',
            'entry_type', 'entry_type_display', 'impact_level', 'impact_level_display',
            'revenue_impact', 'social_proof', 'competitive_advantage',
            'is_featured', 'published_date', 'author_name', 'tags', 'media',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ChangelogFeedbackSerializer(serializers.ModelSerializer):
    """Serializer for changelog feedback."""
    
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = ChangelogFeedback
        fields = [
            'id', 'entry', 'feedback_type', 'comment', 
            'user_name', 'created_at'
        ]
        read_only_fields = ['id', 'user_name', 'created_at']
    
    def create(self, validated_data):
        """Auto-set client and user from request context."""
        request = self.context['request']
        validated_data['client'] = request.tenant
        validated_data['user'] = request.user if request.user.is_authenticated else None
        return super().create(validated_data)


class ChangelogFeedbackCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating feedback."""
    
    class Meta:
        model = ChangelogFeedback
        fields = ['entry', 'feedback_type', 'comment']
    
    def create(self, validated_data):
        """Auto-set client and user from request context."""
        request = self.context['request']
        validated_data['client'] = request.tenant
        validated_data['user'] = request.user if request.user.is_authenticated else None
        return super().create(validated_data)
