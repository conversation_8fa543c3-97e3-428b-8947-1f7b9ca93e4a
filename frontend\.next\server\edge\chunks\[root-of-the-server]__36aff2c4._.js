(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__36aff2c4._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/auth-config.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// lib/auth-config.ts - Clean enterprise configuration
__turbopack_context__.s({
    "AUTH_CONFIG": ()=>AUTH_CONFIG,
    "SESSION_CONFIG": ()=>SESSION_CONFIG,
    "apiClient": ()=>apiClient
});
const AUTH_CONFIG = {
    API_BASE_URL: ("TURBOPACK compile-time value", "http://localhost:8000") || 'http://localhost:8000'
};
const SESSION_CONFIG = {
    COOKIE_NAME: 'auth_token',
    MAX_AGE: 24 * 60 * 60 * 1000,
    SECURE: ("TURBOPACK compile-time value", "development") === 'production',
    SAME_SITE: 'lax'
};
class ApiClient {
    baseUrl;
    constructor(baseUrl){
        this.baseUrl = baseUrl;
    }
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
        // Add auth token if available
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        const response = await fetch(url, config);
        if (!response.ok) {
            const errorData = await response.json().catch(()=>({}));
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
        }
        return response.json();
    }
    async login(data) {
        return this.request('/api/auth/login/', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    async register(data) {
        return this.request('/api/auth/register/', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    async logout() {
        await this.request('/api/auth/logout/', {
            method: 'POST'
        });
    }
    async refreshToken() {
        return this.request('/api/auth/refresh/', {
            method: 'POST'
        });
    }
}
const apiClient = new ApiClient(AUTH_CONFIG.API_BASE_URL);
}),
"[project]/src/lib/auth/server.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// lib/auth/server.ts - Fixed export
__turbopack_context__.s({
    "getServerSession": ()=>getServerSession,
    "requireAuthentication": ()=>requireAuthentication,
    "validateTenantAccess": ()=>validateTenantAccess,
    "validateTokenWithBackend": ()=>validateTokenWithBackend
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$headers$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/api/headers.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/server/request/cookies.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-config.ts [middleware-edge] (ecmascript)");
;
;
async function getServerSession() {
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$request$2f$cookies$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["cookies"])();
        const token = cookieStore.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SESSION_CONFIG"].COOKIE_NAME)?.value;
        if (!token) {
            return null;
        }
        return await validateTokenWithBackend(token);
    } catch (error) {
        console.error('Server session validation failed:', error);
        return null;
    }
}
async function validateTokenWithBackend(token) {
    try {
        const controller = new AbortController();
        const timeout = setTimeout(()=>controller.abort(), 10000);
        const response = await fetch(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["AUTH_CONFIG"].API_BASE_URL}/api/auth/validate/`, {
            method: 'GET',
            headers: {
                'Authorization': `Token ${token}`,
                'Content-Type': 'application/json'
            },
            cache: 'no-store',
            signal: controller.signal
        });
        clearTimeout(timeout);
        if (!response.ok) {
            return null;
        }
        const data = await response.json();
        return {
            user: data.user,
            client: data.client,
            token
        };
    } catch (error) {
        console.error('Token validation failed:', error);
        return null;
    }
}
async function validateTenantAccess(session, tenantSlug) {
    return session.client.slug === tenantSlug;
}
async function requireAuthentication(tenantSlug) {
    const session = await getServerSession();
    if (!session) {
        throw new Error('Authentication required');
    }
    const validTenant = await validateTenantAccess(session, tenantSlug);
    if (!validTenant) {
        throw new Error('Tenant access denied');
    }
    return session;
}
}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// middleware.ts - Enhanced and constitutional compliant
__turbopack_context__.s({
    "config": ()=>config,
    "middleware": ()=>middleware
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$server$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/server.ts [middleware-edge] (ecmascript)");
;
;
async function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip middleware for public paths
    const publicPaths = [
        '/login',
        '/register',
        '/api',
        '/_next',
        '/favicon.ico',
        '/unauthorized',
        '/'
    ];
    if (publicPaths.some((path)=>pathname.startsWith(path))) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Only protect tenant routes (format: /[tenantSlug]/*)
    const tenantRouteMatch = pathname.match(/^\/([^\/]+)\/(.+)/);
    if (!tenantRouteMatch) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    const [, tenantSlug] = tenantRouteMatch;
    // Simple authentication check
    const authToken = request.cookies.get('auth_token')?.value;
    if (!authToken) {
        console.warn('[Middleware] No auth token found, redirecting to login', {
            path: pathname,
            tenantSlug
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));
    }
    try {
        // Validate session with backend
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$server$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["validateTokenWithBackend"])(authToken);
        if (!session) {
            console.warn('[Middleware] Invalid session, redirecting to login', {
                path: pathname,
                tenantSlug
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));
        }
        if (session.client.slug !== tenantSlug) {
            console.error('[Middleware] Tenant mismatch, redirecting to login', {
                path: pathname,
                expected: tenantSlug,
                actual: session.client.slug,
                user: session.user.email
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));
        }
        // Success - allow access
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    } catch (error) {
        console.error('[Middleware] Authentication error', {
            path: pathname,
            tenantSlug,
            error: error instanceof Error ? error.message : String(error)
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${tenantSlug}/login?redirect=${pathname}`, request.url));
    }
}
const config = {
    matcher: [
        '/((?!api|_next/static|_next/image|favicon.ico|login|register).*)'
    ]
};
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__36aff2c4._.js.map