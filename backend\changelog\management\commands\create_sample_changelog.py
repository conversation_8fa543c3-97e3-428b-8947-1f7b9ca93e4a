"""
Management command to create sample changelog entries.

Creates business-friendly changelog entries following the verbiage guidelines
with psychological warfare elements and proper tenant isolation.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timed<PERSON>ta
import random

from tenants.models import Client
from changelog.models import ChangelogEntry, ChangelogTag


class Command(BaseCommand):
    help = 'Create sample changelog entries with business-friendly language'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-slug',
            type=str,
            help='Create entries for specific tenant (leave empty for global entries)',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of entries to create (default: 10)',
        )

    def handle(self, *args, **options):
        tenant_slug = options.get('tenant_slug')
        count = options['count']
        
        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(f'Created admin user: admin/admin123')

        # Get tenant if specified
        client = None
        if tenant_slug:
            try:
                client = Client.objects.get(slug=tenant_slug)
                self.stdout.write(f'Creating entries for tenant: {client.name}')
            except Client.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Tenant "{tenant_slug}" not found')
                )
                return
        else:
            self.stdout.write('Creating global changelog entries')

        # Create tags
        tags_data = [
            {'name': 'Performance', 'color': '#10B981', 'description': 'Speed and efficiency improvements'},
            {'name': 'Security', 'color': '#EF4444', 'description': 'Security enhancements and fixes'},
            {'name': 'UI/UX', 'color': '#8B5CF6', 'description': 'User interface and experience improvements'},
            {'name': 'Analytics', 'color': '#F59E0B', 'description': 'Analytics and reporting features'},
            {'name': 'Integration', 'color': '#06B6D4', 'description': 'Third-party integrations'},
            {'name': 'Mobile', 'color': '#EC4899', 'description': 'Mobile experience improvements'},
        ]
        
        created_tags = []
        for tag_data in tags_data:
            tag, created = ChangelogTag.objects.get_or_create(
                name=tag_data['name'],
                defaults={
                    'color': tag_data['color'],
                    'description': tag_data['description']
                }
            )
            created_tags.append(tag)
            if created:
                self.stdout.write(f'Created tag: {tag.name}')

        # Sample changelog entries with business-friendly language
        sample_entries = [
            {
                'title': 'Lightning-Fast Report Loading',
                'business_benefit': 'Your monthly SEO reports now load 3x faster, so you can spend less time waiting and more time acting on insights that grow your business.',
                'action_required': 'Nothing - it just works! Your reports are automatically faster.',
                'entry_type': 'improvement',
                'impact_level': 'high',
                'revenue_impact': 'Save 2 hours per week',
                'social_proof': 'Join 1,200+ businesses already benefiting from faster reports',
                'competitive_advantage': 'While competitors wait for slow reports, you\'re already implementing winning strategies.',
                'tags': ['Performance', 'Analytics'],
                'is_featured': True,
            },
            {
                'title': 'One-Click Competitor Analysis',
                'business_benefit': 'Discover what your competitors are doing right (and wrong) with a single click. No more manual research - get instant competitive intelligence.',
                'action_required': 'Try it in your dashboard under "Competitive Intelligence" - it\'s the new blue button.',
                'entry_type': 'new',
                'impact_level': 'high',
                'revenue_impact': 'Identify $5,000+ in missed opportunities',
                'social_proof': 'Over 800 businesses have already discovered competitor secrets',
                'competitive_advantage': 'Know your competitors\' strategies before they know yours.',
                'tags': ['Analytics', 'Integration'],
                'is_featured': True,
            },
            {
                'title': 'Smart Keyword Suggestions',
                'business_benefit': 'Our AI now suggests high-value keywords your competitors are missing. Stop guessing what to rank for - let data guide your strategy.',
                'action_required': 'Check your keyword dashboard for new AI-powered suggestions marked with a ✨ icon.',
                'entry_type': 'new',
                'impact_level': 'medium',
                'revenue_impact': 'Target keywords worth $2,000+ monthly',
                'social_proof': 'AI suggestions have helped 500+ businesses find profitable keywords',
                'competitive_advantage': 'Rank for keywords your competitors haven\'t discovered yet.',
                'tags': ['Analytics', 'Performance'],
            },
            {
                'title': 'Mobile Dashboard Perfection',
                'business_benefit': 'Manage your SEO empire from anywhere. Our mobile dashboard is now as powerful as desktop - perfect for busy business owners on the go.',
                'action_required': 'Open your dashboard on your phone - everything just works beautifully now.',
                'entry_type': 'improvement',
                'impact_level': 'medium',
                'social_proof': 'Mobile users report 40% more engagement with their SEO data',
                'competitive_advantage': 'Monitor and respond to SEO opportunities instantly, even when away from your desk.',
                'tags': ['Mobile', 'UI/UX'],
            },
            {
                'title': 'Bank-Level Security Upgrade',
                'business_benefit': 'Your business data is now protected with the same security banks use. Sleep better knowing your competitive intelligence is fortress-secure.',
                'action_required': 'Nothing - your account is automatically more secure. You might notice a new security badge in your profile.',
                'entry_type': 'improvement',
                'impact_level': 'high',
                'competitive_advantage': 'Your SEO data stays private while competitors use less secure tools.',
                'tags': ['Security'],
                'is_featured': True,
            },
            {
                'title': 'Fixed: Ranking Data Sync Issues',
                'business_benefit': 'Your ranking data now updates perfectly every time. No more wondering if your numbers are current - trust your data completely.',
                'action_required': 'Nothing - rankings sync automatically and accurately now.',
                'entry_type': 'bugfix',
                'impact_level': 'medium',
                'tags': ['Performance', 'Analytics'],
            },
            {
                'title': 'Google Analytics 4 Deep Integration',
                'business_benefit': 'See exactly which SEO efforts drive real revenue. Connect your GA4 data to understand the true ROI of every keyword and page.',
                'action_required': 'Visit Settings > Integrations to connect your Google Analytics 4 account in under 2 minutes.',
                'entry_type': 'new',
                'impact_level': 'high',
                'revenue_impact': 'Track $10,000+ in SEO-driven revenue',
                'social_proof': 'Businesses with GA4 integration see 60% better ROI tracking',
                'competitive_advantage': 'Prove SEO ROI while competitors guess at their results.',
                'tags': ['Integration', 'Analytics'],
            },
            {
                'title': 'Instant Alerts for Ranking Drops',
                'business_benefit': 'Never lose rankings without knowing. Get instant alerts when your positions drop, so you can fix problems before they hurt your business.',
                'action_required': 'Enable alerts in your notification settings - we recommend starting with your top 10 keywords.',
                'entry_type': 'new',
                'impact_level': 'high',
                'competitive_advantage': 'Respond to ranking changes in hours, not weeks like your competitors.',
                'tags': ['Analytics', 'Performance'],
            },
            {
                'title': 'Beautiful PDF Reports for Clients',
                'business_benefit': 'Impress clients and stakeholders with stunning, professional SEO reports. Export any data as a beautiful PDF that makes you look like the expert you are.',
                'action_required': 'Look for the new "Export PDF" button on any report page.',
                'entry_type': 'new',
                'impact_level': 'medium',
                'social_proof': 'Agencies using PDF reports win 30% more client renewals',
                'competitive_advantage': 'Present data so professionally that clients never want to leave.',
                'tags': ['UI/UX', 'Analytics'],
            },
            {
                'title': 'Major Platform Expansion Coming',
                'business_benefit': 'We\'re launching 5 new features next month that will revolutionize how you dominate local search. Get ready for your biggest competitive advantage yet.',
                'action_required': 'Stay tuned - we\'ll email you first when these game-changing features launch.',
                'entry_type': 'announcement',
                'impact_level': 'high',
                'social_proof': 'Over 2,000 businesses are on the early access list',
                'competitive_advantage': 'Be among the first to use features that will take competitors months to copy.',
                'tags': ['Analytics', 'Integration'],
                'is_featured': True,
            },
        ]

        # Create entries
        created_count = 0
        for i in range(min(count, len(sample_entries))):
            entry_data = sample_entries[i]
            
            # Create the entry
            entry = ChangelogEntry.objects.create(
                client=client,
                title=entry_data['title'],
                business_benefit=entry_data['business_benefit'],
                action_required=entry_data['action_required'],
                entry_type=entry_data['entry_type'],
                impact_level=entry_data['impact_level'],
                revenue_impact=entry_data.get('revenue_impact', ''),
                social_proof=entry_data.get('social_proof', ''),
                competitive_advantage=entry_data.get('competitive_advantage', ''),
                is_published=True,
                is_featured=entry_data.get('is_featured', False),
                published_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                author=admin_user,
            )
            
            # Add tags
            tag_names = entry_data.get('tags', [])
            for tag_name in tag_names:
                tag = next((t for t in created_tags if t.name == tag_name), None)
                if tag:
                    entry.tags.add(tag)
            
            created_count += 1
            self.stdout.write(f'Created entry: {entry.title}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} changelog entries'
            )
        )
