"""
Changelog Models - Business-friendly changelog system for small business owners.

Following the verbiage guidelines for clear, actionable communication that focuses
on business benefits rather than technical details.
"""

from django.db import models
from django.contrib.auth.models import User
from tenants.models import Client


class ChangelogTag(models.Model):
    """
    Tags for categorizing changelog entries (e.g., 'Performance', 'Security', 'UI').
    """
    name = models.CharField(max_length=50, unique=True)
    color = models.CharField(
        max_length=7,
        default='#3B82F6',
        help_text="Hex color code for tag display"
    )
    description = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class ChangelogEntry(models.Model):
    """
    Main changelog entry following the business-friendly format:
    - What Changed (title with emoji)
    - Why You'll Love It (business benefit)
    - What To Do (action required)
    """

    ENTRY_TYPES = [
        ('new', '🆕 New Feature'),
        ('improvement', '⚡ Improvement'),
        ('bugfix', '🐛 Bug Fix'),
        ('announcement', '📢 Announcement'),
    ]

    IMPACT_LEVELS = [
        ('high', 'High Impact'),
        ('medium', 'Medium Impact'),
        ('low', 'Low Impact'),
    ]

    # Tenant isolation - required for all tenant-specific data
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        help_text="Tenant that owns this changelog entry",
        null=True,  # Allow global entries for platform-wide updates
        blank=True
    )

    # Core content following their verbiage format
    title = models.CharField(
        max_length=200,
        help_text="What Changed - Clear, actionable title (e.g., 'Faster report loading')"
    )

    business_benefit = models.TextField(
        help_text="Why You'll Love It - Focus on business value, not technical details"
    )

    action_required = models.TextField(
        help_text="What To Do - Clear instructions for users (can be 'Nothing - it just works!')"
    )

    # Classification
    entry_type = models.CharField(
        max_length=20,
        choices=ENTRY_TYPES,
        help_text="Type of update with emoji prefix"
    )

    impact_level = models.CharField(
        max_length=10,
        choices=IMPACT_LEVELS,
        help_text="Business impact level for prioritization"
    )

    # Psychological warfare elements (following their philosophy)
    revenue_impact = models.CharField(
        max_length=100,
        blank=True,
        help_text="Specific revenue benefit (e.g., 'Save 2 hours per week', '$500/month savings')"
    )

    social_proof = models.CharField(
        max_length=200,
        blank=True,
        help_text="Social validation (e.g., 'Join 500+ businesses already using this')"
    )

    competitive_advantage = models.TextField(
        blank=True,
        help_text="How this gives them an edge over competitors"
    )

    # Publishing controls
    is_published = models.BooleanField(
        default=False,
        help_text="Whether this entry is visible to users"
    )

    is_featured = models.BooleanField(
        default=False,
        help_text="Highlight this entry (for major updates)"
    )

    published_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this was published (auto-set when is_published=True)"
    )

    # Metadata
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        help_text="Team member who created this entry"
    )

    tags = models.ManyToManyField(
        ChangelogTag,
        blank=True,
        help_text="Categories for filtering and organization"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-published_date', '-created_at']
        verbose_name = "Changelog Entry"
        verbose_name_plural = "Changelog Entries"

    def __str__(self):
        emoji = dict(self.ENTRY_TYPES).get(self.entry_type, '📝')
        return f"{emoji} {self.title}"

    def save(self, *args, **kwargs):
        # Auto-set published_date when publishing
        if self.is_published and not self.published_date:
            from django.utils import timezone
            self.published_date = timezone.now()
        elif not self.is_published:
            self.published_date = None

        super().save(*args, **kwargs)


class ChangelogMedia(models.Model):
    """
    Screenshots, GIFs, or videos to accompany changelog entries.
    Following their emphasis on visual, scannable content.
    """

    MEDIA_TYPES = [
        ('image', 'Screenshot/Image'),
        ('gif', 'Animated GIF'),
        ('video', 'Video'),
    ]

    entry = models.ForeignKey(
        ChangelogEntry,
        on_delete=models.CASCADE,
        related_name='media'
    )

    media_type = models.CharField(max_length=10, choices=MEDIA_TYPES)
    file = models.FileField(upload_to='changelog/media/')
    alt_text = models.CharField(
        max_length=200,
        help_text="Accessibility description of the media"
    )
    caption = models.TextField(
        blank=True,
        help_text="Optional caption explaining what's shown"
    )

    order = models.PositiveIntegerField(
        default=0,
        help_text="Display order for multiple media items"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.get_media_type_display()} for {self.entry.title}"


class ChangelogFeedback(models.Model):
    """
    User feedback on changelog entries ("Was this helpful?" tracking).
    Tenant-aware to track feedback per client.
    """

    FEEDBACK_TYPES = [
        ('helpful', 'Helpful'),
        ('not_helpful', 'Not Helpful'),
        ('suggestion', 'Suggestion'),
    ]

    entry = models.ForeignKey(
        ChangelogEntry,
        on_delete=models.CASCADE,
        related_name='feedback'
    )

    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        help_text="Tenant that provided this feedback"
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="User who provided feedback (if logged in)"
    )

    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPES)
    comment = models.TextField(
        blank=True,
        help_text="Optional detailed feedback"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('entry', 'client', 'user')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_feedback_type_display()} on {self.entry.title}"
