# Component Patterns & Architecture

## Overview

The SEO Dashboard follows a structured component architecture based on Next.js 15 patterns, emphasizing Server Components, clear separation of concerns, and multi-tenant awareness. This document outlines the established patterns and conventions.

## Component Classification

### Server Components (Default)
**Purpose**: Handle data fetching, authentication, and initial rendering
**Location**: `components/server/` or default component files
**Characteristics**:
- Async functions
- Direct database/API access
- No client-side interactivity
- Better SEO and performance

```tsx
// Example: Server Component
export async function DashboardMetrics({ tenantSlug }: { tenantSlug: string }) {
  // Server-side data fetching
  const metrics = await fetchDashboardMetrics(tenantSlug);
  
  return (
    <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
      {metrics.map((metric) => (
        <MetricCard key={metric.id} metric={metric} />
      ))}
    </div>
  );
}
```

### Client Components
**Purpose**: Handle user interactions, state management, and dynamic behavior
**Location**: `components/client/`
**Characteristics**:
- `'use client'` directive
- Event handlers and state
- Browser APIs access
- Interactive features

```tsx
'use client';

import { useState } from 'react';

export function InteractiveChart({ data }: { data: ChartData[] }) {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  
  return (
    <div className='space-y-4'>
      <div className='flex space-x-2'>
        {periods.map((period) => (
          <button
            key={period.value}
            onClick={() => setSelectedPeriod(period.value)}
            className={cn(
              'px-3 py-1 rounded-md text-sm',
              selectedPeriod === period.value
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:text-gray-900'
            )}
          >
            {period.label}
          </button>
        ))}
      </div>
      <Chart data={data} period={selectedPeriod} />
    </div>
  );
}
```

### Shared Components
**Purpose**: Reusable UI components and utilities
**Location**: `components/shared/` or `components/ui/`
**Characteristics**:
- No React dependencies
- Pure functions or simple components
- Type definitions and utilities

## File Organization Patterns

### Feature-Based Structure
```
features/
├── dashboard/
│   ├── components/
│   │   ├── server/
│   │   │   ├── dashboard-content.tsx
│   │   │   ├── metrics-overview.tsx
│   │   │   └── recent-activity.tsx
│   │   ├── client/
│   │   │   ├── interactive-chart.tsx
│   │   │   └── filter-controls.tsx
│   │   └── shared/
│   │       ├── types.ts
│   │       └── utils.ts
│   └── lib/
│       ├── actions/
│       │   └── dashboard-actions.ts
│       ├── utils/
│       │   └── dashboard-utils.ts
│       └── validators/
│           └── dashboard-schemas.ts
```

### Component Naming Conventions
- **Server Components**: PascalCase, descriptive names (`DashboardMetrics`)
- **Client Components**: PascalCase with action indication (`InteractiveChart`)
- **Shared Components**: PascalCase, generic names (`Button`, `Card`)
- **Files**: kebab-case matching component name (`dashboard-metrics.tsx`)

## Multi-Tenant Component Patterns

### Tenant-Aware Server Components
```tsx
interface TenantAwareProps {
  tenantSlug: string;
}

export async function TenantDashboard({ tenantSlug }: TenantAwareProps) {
  // Validate tenant access
  const session = await getServerSession();
  await validateTenantAccess(session, tenantSlug);
  
  // Fetch tenant-scoped data
  const dashboardData = await fetchTenantDashboard(tenantSlug);
  
  return (
    <div className='space-y-6'>
      <DashboardHeader tenant={dashboardData.tenant} />
      <DashboardMetrics data={dashboardData.metrics} />
      <DashboardCharts data={dashboardData.charts} />
    </div>
  );
}
```

### Tenant Context Propagation
```tsx
'use client';

import { useTenant } from '@/components/providers/tenant-client-provider';

export function TenantAwareClientComponent() {
  const { session, tenantSlug, hasAccess } = useTenant();
  
  if (!hasAccess('analytics')) {
    return <AccessDenied />;
  }
  
  return (
    <div>
      <h2>Analytics for {session.client.name}</h2>
      {/* Component content */}
    </div>
  );
}
```

## Data Fetching Patterns

### Server-Side Data Fetching
```tsx
// In Server Components
export async function ServerDataComponent({ tenantSlug }: { tenantSlug: string }) {
  // Direct API calls or database queries
  const data = await fetch(`${process.env.API_URL}/api/${tenantSlug}/metrics`, {
    headers: {
      'Authorization': `Bearer ${await getServerToken()}`,
    },
  });
  
  if (!data.ok) {
    throw new Error('Failed to fetch data');
  }
  
  const metrics = await data.json();
  
  return <MetricsDisplay metrics={metrics} />;
}
```

### Client-Side Data Fetching
```tsx
'use client';

import { useEffect, useState } from 'react';
import { useTenant } from '@/components/providers/tenant-client-provider';

export function ClientDataComponent() {
  const { tenantSlug } = useTenant();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`/api/${tenantSlug}/live-data`);
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [tenantSlug]);
  
  if (loading) return <LoadingSpinner />;
  
  return <DataDisplay data={data} />;
}
```

## Error Handling Patterns

### Server Component Error Boundaries
```tsx
// error.tsx in app directory
'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className='flex flex-col items-center justify-center min-h-[400px] space-y-4'>
      <h2 className='text-xl font-semibold text-gray-900'>Something went wrong!</h2>
      <p className='text-gray-600 text-center max-w-md'>
        We encountered an error while loading this page. Please try again.
      </p>
      <button
        onClick={reset}
        className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700'
      >
        Try again
      </button>
    </div>
  );
}
```

### Client Component Error Handling
```tsx
'use client';

import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }: any) {
  return (
    <div className='p-4 border border-red-200 rounded-md bg-red-50'>
      <h3 className='text-red-800 font-medium'>Something went wrong</h3>
      <p className='text-red-600 text-sm mt-1'>{error.message}</p>
      <button
        onClick={resetErrorBoundary}
        className='mt-2 text-sm text-red-600 underline hover:text-red-800'
      >
        Try again
      </button>
    </div>
  );
}

export function ClientComponentWithErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      {children}
    </ErrorBoundary>
  );
}
```

## Loading States & Suspense

### Server Component Loading
```tsx
// loading.tsx in app directory
export default function Loading() {
  return (
    <div className='space-y-6'>
      <div className='animate-pulse'>
        <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
          {[...Array(4)].map((_, i) => (
            <div key={i} className='h-24 bg-gray-200 rounded'></div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

### Suspense Boundaries
```tsx
import { Suspense } from 'react';

export function DashboardPage({ tenantSlug }: { tenantSlug: string }) {
  return (
    <div className='space-y-6'>
      <Suspense fallback={<MetricsLoading />}>
        <DashboardMetrics tenantSlug={tenantSlug} />
      </Suspense>
      
      <Suspense fallback={<ChartsLoading />}>
        <DashboardCharts tenantSlug={tenantSlug} />
      </Suspense>
    </div>
  );
}
```

## Form Patterns

### Server Actions
```tsx
// Server Action
async function updateDashboardSettings(formData: FormData) {
  'use server';
  
  const tenantSlug = formData.get('tenantSlug') as string;
  const settings = {
    refreshInterval: formData.get('refreshInterval'),
    defaultView: formData.get('defaultView'),
  };
  
  // Validate tenant access
  const session = await getServerSession();
  await validateTenantAccess(session, tenantSlug);
  
  // Update settings
  await updateTenantSettings(tenantSlug, settings);
  
  revalidatePath(`/${tenantSlug}/dashboard`);
}

// Form Component
export function SettingsForm({ tenantSlug }: { tenantSlug: string }) {
  return (
    <form action={updateDashboardSettings}>
      <input type='hidden' name='tenantSlug' value={tenantSlug} />
      <div className='space-y-4'>
        <div>
          <label htmlFor='refreshInterval' className='block text-sm font-medium'>
            Refresh Interval
          </label>
          <select name='refreshInterval' className='mt-1 block w-full rounded-md border-gray-300'>
            <option value='30'>30 seconds</option>
            <option value='60'>1 minute</option>
            <option value='300'>5 minutes</option>
          </select>
        </div>
        <button
          type='submit'
          className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700'
        >
          Save Settings
        </button>
      </div>
    </form>
  );
}
```

## Styling Patterns

### Tailwind CSS Conventions
```tsx
// Consistent spacing and sizing
const spacing = {
  xs: 'p-2',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-12',
};

// Color system
const colors = {
  primary: 'bg-blue-600 text-white',
  secondary: 'bg-gray-100 text-gray-900',
  success: 'bg-green-600 text-white',
  warning: 'bg-yellow-600 text-white',
  danger: 'bg-red-600 text-white',
};

// Component with consistent styling
export function StatusCard({ status, children }: { status: keyof typeof colors; children: React.ReactNode }) {
  return (
    <div className={cn(
      'rounded-lg shadow-sm border',
      spacing.md,
      colors[status]
    )}>
      {children}
    </div>
  );
}
```

### CSS Modules (When Needed)
```css
/* dashboard.module.css */
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
  }
}
```

## Testing Patterns

### Component Testing
```tsx
// __tests__/dashboard-metrics.test.tsx
import { render, screen } from '@testing-library/react';
import { DashboardMetrics } from '../dashboard-metrics';

// Mock tenant provider
jest.mock('@/components/providers/tenant-client-provider', () => ({
  useTenant: () => ({
    tenantSlug: 'test-tenant',
    session: { client: { name: 'Test Client' } },
  }),
}));

describe('DashboardMetrics', () => {
  it('renders metrics correctly', async () => {
    const mockMetrics = [
      { id: '1', title: 'Total Visitors', value: '1,234', change: '+12%' },
      { id: '2', title: 'Page Views', value: '5,678', change: '+8%' },
    ];
    
    render(<DashboardMetrics metrics={mockMetrics} />);
    
    expect(screen.getByText('Total Visitors')).toBeInTheDocument();
    expect(screen.getByText('1,234')).toBeInTheDocument();
    expect(screen.getByText('+12%')).toBeInTheDocument();
  });
});
```

## Performance Optimization Patterns

### Code Splitting
```tsx
import dynamic from 'next/dynamic';

// Lazy load heavy components
const HeavyChart = dynamic(() => import('./heavy-chart'), {
  loading: () => <ChartSkeleton />,
  ssr: false, // Disable SSR for client-only components
});

export function DashboardPage() {
  return (
    <div>
      <DashboardHeader />
      <Suspense fallback={<ChartSkeleton />}>
        <HeavyChart />
      </Suspense>
    </div>
  );
}
```

### Memoization
```tsx
import { memo } from 'react';

// Memoize expensive components
export const ExpensiveComponent = memo(function ExpensiveComponent({ 
  data, 
  onUpdate 
}: { 
  data: ComplexData; 
  onUpdate: (id: string) => void; 
}) {
  // Expensive rendering logic
  return <ComplexVisualization data={data} onUpdate={onUpdate} />;
});
```

## Best Practices Summary

### Component Design
1. **Single Responsibility**: Each component has one clear purpose
2. **Composition over Inheritance**: Build complex UIs from simple components
3. **Props Interface**: Clear, typed interfaces for all components
4. **Error Boundaries**: Wrap components that might fail

### Performance
1. **Server Components First**: Use Server Components by default
2. **Client Components Sparingly**: Only when interactivity is needed
3. **Code Splitting**: Lazy load non-critical components
4. **Memoization**: Prevent unnecessary re-renders

### Maintainability
1. **Consistent Naming**: Follow established conventions
2. **Type Safety**: Use TypeScript for all components
3. **Documentation**: Comment complex logic and patterns
4. **Testing**: Unit tests for critical components
