# seo_dashboard/urls.py
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from seo_data.test_views import (
    test_backend_connection,
    test_industry_detection_simple,
    test_education_intelligence_simple
)

def health_check(request):
    return JsonResponse({'status': 'ok'})

urlpatterns = [
    path('admin/', admin.site.urls),
    path('health/', health_check, name='health_check'),

    # Test endpoints (no tenant required) - MUST come before tenant routes
    path('api/test/backend/', test_backend_connection, name='test-backend'),
    path('api/test/industry/', test_industry_detection_simple, name='test-industry'),
    path('api/test/education/', test_education_intelligence_simple, name='test-education'),

    # Main tenant API route (no dashboard prefix)
    path('api/<slug:tenant_slug>/', include('seo_data.urls')),

    # Subscription management endpoints
    path('api/<slug:tenant_slug>/subscription/', include('tenants.subscription_urls')),

    # Integration endpoints
    path('api/<slug:tenant_slug>/integrations/', include('integrations.urls')),

    # Changelog endpoints
    path('api/<slug:tenant_slug>/changelog/', include('changelog.urls')),

    # Fix: Capture tenant_slug but don't pass it to the view
    path('<slug:tenant_slug>/seo/', include('seo_data.urls')),
    path('<slug:tenant_slug>/insights/', include('ai_insights.urls')),

    # Public API endpoints (no tenant required)
    path('api/auth/', include('tenants.urls')),  # If you have this
]
