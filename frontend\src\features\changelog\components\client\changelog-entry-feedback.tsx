'use client';

/**
 * Changelog Entry Feedback - "Was this helpful?" feedback component.
 * 
 * Client component that allows users to provide feedback on changelog entries
 * following their business-friendly approach.
 */

import { useState } from 'react';
import { submitChangelogFeedback } from '@/lib/api/changelog';

interface ChangelogEntryFeedbackProps {
  tenantSlug: string;
  entryId: number;
}

export function ChangelogEntryFeedback({ tenantSlug, entryId }: ChangelogEntryFeedbackProps) {
  const [feedback, setFeedback] = useState<'helpful' | 'not_helpful' | 'suggestion' | null>(null);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCommentBox, setShowCommentBox] = useState(false);

  const handleFeedbackSubmit = async (feedbackType: 'helpful' | 'not_helpful' | 'suggestion') => {
    if (isSubmitted) return;

    setFeedback(feedbackType);
    setError(null);

    // Show comment box for suggestions or negative feedback
    if (feedbackType === 'suggestion' || feedbackType === 'not_helpful') {
      setShowCommentBox(true);
      return;
    }

    // Submit immediately for positive feedback
    await submitFeedback(feedbackType, '');
  };

  const submitFeedback = async (feedbackType: 'helpful' | 'not_helpful' | 'suggestion', commentText: string) => {
    setIsSubmitting(true);
    setError(null);

    try {
      await submitChangelogFeedback(tenantSlug, entryId, {
        feedback_type: feedbackType,
        comment: commentText.trim() || undefined,
      });

      setIsSubmitted(true);
      setShowCommentBox(false);
    } catch (error: any) {
      if (error.message?.includes('already provided feedback')) {
        setError('You\'ve already provided feedback for this update. Thanks for your input!');
        setIsSubmitted(true);
      } else {
        setError('Failed to submit feedback. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSubmit = async () => {
    if (!feedback) return;
    await submitFeedback(feedback, comment);
  };

  if (isSubmitted) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center">
          <div className="text-4xl mb-3">🙏</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Thanks for your feedback!
          </h3>
          <p className="text-gray-600">
            Your input helps us build features that give your business an even bigger advantage.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Was this update helpful for your business?
        </h3>
        <p className="text-gray-600 text-sm">
          Your feedback helps us prioritize features that give you the biggest competitive advantage.
        </p>
      </div>

      {!showCommentBox ? (
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => handleFeedbackSubmit('helpful')}
            disabled={isSubmitting}
            className="flex items-center space-x-2 px-6 py-3 bg-green-50 text-green-700 rounded-lg border border-green-200 hover:bg-green-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="text-xl">👍</span>
            <span className="font-medium">Yes, this helps my business!</span>
          </button>

          <button
            onClick={() => handleFeedbackSubmit('not_helpful')}
            disabled={isSubmitting}
            className="flex items-center space-x-2 px-6 py-3 bg-gray-50 text-gray-700 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="text-xl">👎</span>
            <span className="font-medium">Not really</span>
          </button>

          <button
            onClick={() => handleFeedbackSubmit('suggestion')}
            disabled={isSubmitting}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-700 rounded-lg border border-blue-200 hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="text-xl">💡</span>
            <span className="font-medium">I have a suggestion</span>
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <label htmlFor="feedback-comment" className="block text-sm font-medium text-gray-700 mb-2">
              {feedback === 'suggestion' 
                ? 'What would make this even better for your business?' 
                : 'How can we improve this to better serve your business?'
              }
            </label>
            <textarea
              id="feedback-comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="Your feedback helps us build features that give you an unfair advantage over competitors..."
              disabled={isSubmitting}
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => {
                setShowCommentBox(false);
                setFeedback(null);
                setComment('');
              }}
              disabled={isSubmitting}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              onClick={handleCommentSubmit}
              disabled={isSubmitting || !comment.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}
    </div>
  );
}
