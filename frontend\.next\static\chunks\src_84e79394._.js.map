{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';\r\nimport { AuthResponse, LoginCredentials, RegisterData } from '@/types/auth';\r\n\r\n// API configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\r\n\r\n// Create axios instance with default config\r\nconst createApiClient = (): AxiosInstance => {\r\n  const client = axios.create({\r\n    baseURL: API_BASE_URL,\r\n    timeout: 10000,\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n\r\n  // Request interceptor to add auth token and tenant context\r\n  client.interceptors.request.use(\r\n    (config) => {\r\n      // Get token from localStorage\r\n      const token = localStorage.getItem('auth_token');\r\n      if (token) {\r\n        config.headers.Authorization = `Token ${token}`;\r\n      }\r\n\r\n      // Add tenant slug to headers if available\r\n      const tenantSlug = localStorage.getItem('tenant_slug');\r\n      if (tenantSlug) {\r\n        config.headers['X-Tenant-Slug'] = tenantSlug;\r\n      }\r\n\r\n      return config;\r\n    },\r\n    (error) => {\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  // Response interceptor for error handling\r\n  client.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      if (error.response?.status === 401) {\r\n        // Clear auth data and redirect to login\r\n        localStorage.removeItem('auth_token');\r\n        localStorage.removeItem('refresh_token');\r\n        localStorage.removeItem('tenant_slug');\r\n        window.location.href = '/login';\r\n      }\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  return client;\r\n};\r\n\r\n// Create the main API client\r\nexport const apiClient = createApiClient();\r\n\r\n// Authentication API endpoints\r\nexport const authApi = {\r\n  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {\r\n    const response = await apiClient.post('/api/auth/login/', credentials);\r\n    return response.data;\r\n  },\r\n\r\n  register: async (data: RegisterData): Promise<AuthResponse> => {\r\n    const response = await apiClient.post('/api/auth/register/', data);\r\n    return response.data;\r\n  },\r\n\r\n  logout: async (): Promise<void> => {\r\n    await apiClient.post('/api/auth/logout/');\r\n  },\r\n\r\n  refreshToken: async (refreshToken: string): Promise<{ token: string }> => {\r\n    const response = await apiClient.post('/api/auth/refresh/', {\r\n      refresh_token: refreshToken,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  validateTenant: async (slug: string): Promise<{ valid: boolean; client: any }> => {\r\n    const response = await apiClient.get(`/api/tenants/${slug}/validate/`);\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// SEO Data API endpoints\r\nexport const seoApi = {\r\n  getMetrics: async (slug: string) => {\r\n    const response = await apiClient.get(`/${slug}/seo/metrics/`);\r\n    return response.data;\r\n  },\r\n\r\n  getKeywords: async (slug: string, params?: any) => {\r\n    const response = await apiClient.get(`/${slug}/seo/keywords/`, { params });\r\n    return response.data;\r\n  },\r\n\r\n  getTrafficData: async (slug: string, dateRange?: string) => {\r\n    const response = await apiClient.get(`/${slug}/seo/traffic/`, {\r\n      params: { date_range: dateRange },\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  getCompetitors: async (slug: string) => {\r\n    const response = await apiClient.get(`/${slug}/seo/competitors/`);\r\n    return response.data;\r\n  },\r\n\r\n  getPagePerformance: async (slug: string) => {\r\n    const response = await apiClient.get(`/${slug}/seo/pages/`);\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// AI Insights API endpoints\r\nexport const insightsApi = {\r\n  getInsights: async (slug: string, params?: any) => {\r\n    const response = await apiClient.get(`/${slug}/insights/`, { params });\r\n    return response.data;\r\n  },\r\n\r\n  updateInsightStatus: async (slug: string, insightId: string, status: string) => {\r\n    const response = await apiClient.patch(`/${slug}/insights/${insightId}/`, {\r\n      status,\r\n    });\r\n    return response.data;\r\n  },\r\n\r\n  generateInsights: async (slug: string) => {\r\n    const response = await apiClient.post(`/${slug}/insights/generate/`);\r\n    return response.data;\r\n  },\r\n};\r\n\r\n// Utility function to handle API errors\r\nexport const handleApiError = (error: any) => {\r\n  if (error.response) {\r\n    // Server responded with error status\r\n    const message = error.response.data?.message || error.response.data?.detail || 'An error occurred';\r\n    return new Error(message);\r\n  } else if (error.request) {\r\n    // Request was made but no response received\r\n    return new Error('Network error. Please check your connection.');\r\n  } else {\r\n    // Something else happened\r\n    return new Error('An unexpected error occurred.');\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;;AAIqB;AAJrB;;AAGA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,4CAA4C;AAC5C,MAAM,kBAAkB;IACtB,MAAM,SAAS,2LAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC1B,SAAS;QACT,SAAS;QACT,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,2DAA2D;IAC3D,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG;+BAC7B,CAAC;YACC,8BAA8B;YAC9B,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,SAAc,OAAN;YAC1C;YAEA,0CAA0C;YAC1C,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,OAAO,OAAO,CAAC,gBAAgB,GAAG;YACpC;YAEA,OAAO;QACT;;+BACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;;IAGF,0CAA0C;IAC1C,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG;+BAC9B,CAAC,WAAa;;+BACd,CAAC;gBACK;YAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,wCAAwC;gBACxC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YACA,OAAO,QAAQ,MAAM,CAAC;QACxB;;IAGF,OAAO;AACT;AAGO,MAAM,YAAY;AAGlB,MAAM,UAAU;IACrB,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,oBAAoB;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,UAAU,IAAI,CAAC;IACvB;IAEA,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,sBAAsB;YAC1D,eAAe;QACjB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,gBAAoB,OAAL,MAAK;QAC1D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,SAAS;IACpB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,IAAQ,OAAL,MAAK;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO,MAAc;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,IAAQ,OAAL,MAAK,mBAAiB;YAAE;QAAO;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO,MAAc;QACnC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,IAAQ,OAAL,MAAK,kBAAgB;YAC5D,QAAQ;gBAAE,YAAY;YAAU;QAClC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,IAAQ,OAAL,MAAK;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,IAAQ,OAAL,MAAK;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,aAAa,OAAO,MAAc;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC,AAAC,IAAQ,OAAL,MAAK,eAAa;YAAE;QAAO;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB,OAAO,MAAc,WAAmB;QAC3D,MAAM,WAAW,MAAM,UAAU,KAAK,CAAC,AAAC,IAAoB,OAAjB,MAAK,cAAsB,OAAV,WAAU,MAAI;YACxE;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,AAAC,IAAQ,OAAL,MAAK;QAC/C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,MAAM,QAAQ,EAAE;YAEF,sBAAgC;QADhD,qCAAqC;QACrC,MAAM,UAAU,EAAA,uBAAA,MAAM,QAAQ,CAAC,IAAI,cAAnB,2CAAA,qBAAqB,OAAO,OAAI,wBAAA,MAAM,QAAQ,CAAC,IAAI,cAAnB,4CAAA,sBAAqB,MAAM,KAAI;QAC/E,OAAO,IAAI,MAAM;IACnB,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,4CAA4C;QAC5C,OAAO,IAAI,MAAM;IACnB,OAAO;QACL,0BAA0B;QAC1B,OAAO,IAAI,MAAM;IACnB;AACF", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/store/auth.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { persist } from 'zustand/middleware';\r\nimport { AuthState, User, Client, LoginCredentials, RegisterData } from '@/types/auth';\r\nimport { authApi, handleApiError } from '@/lib/api';\r\n\r\ninterface AuthStore extends AuthState {\r\n  // Actions\r\n  login: (credentials: LoginCredentials) => Promise<void>;\r\n  register: (data: RegisterData) => Promise<void>;\r\n  logout: () => void;\r\n  setUser: (user: User) => void;\r\n  setClient: (client: Client) => void;\r\n  setToken: (token: string) => void;\r\n  clearAuth: () => void;\r\n  validateTenant: (slug: string) => Promise<boolean>;\r\n  initializeAuth: () => void;\r\n}\r\n\r\nexport const useAuthStore = create<AuthStore>()(\r\n  persist(\r\n    (set, get) => ({\r\n      // Initial state\r\n      user: null,\r\n      client: null,\r\n      token: null,\r\n      isAuthenticated: false,\r\n      isLoading: false,\r\n\r\n      // Actions\r\n      login: async (credentials: LoginCredentials) => {\r\n        try {\r\n          set({ isLoading: true });\r\n          \r\n          const response = await authApi.login(credentials);\r\n          const { user, client, token, refreshToken } = response;\r\n\r\n          // Store tokens\r\n          localStorage.setItem('auth_token', token);\r\n          localStorage.setItem('refresh_token', refreshToken);\r\n          localStorage.setItem('tenant_slug', client.slug);\r\n\r\n          set({\r\n            user,\r\n            client,\r\n            token,\r\n            isAuthenticated: true,\r\n            isLoading: false,\r\n          });\r\n        } catch (error) {\r\n          set({ isLoading: false });\r\n          throw handleApiError(error);\r\n        }\r\n      },\r\n\r\n      register: async (data: RegisterData) => {\r\n        try {\r\n          set({ isLoading: true });\r\n          \r\n          const response = await authApi.register(data);\r\n          const { user, client, token, refreshToken } = response;\r\n\r\n          // Store tokens\r\n          localStorage.setItem('auth_token', token);\r\n          localStorage.setItem('refresh_token', refreshToken);\r\n          localStorage.setItem('tenant_slug', client.slug);\r\n\r\n          set({\r\n            user,\r\n            client,\r\n            token,\r\n            isAuthenticated: true,\r\n            isLoading: false,\r\n          });\r\n        } catch (error) {\r\n          set({ isLoading: false });\r\n          throw handleApiError(error);\r\n        }\r\n      },\r\n\r\n      logout: async () => {\r\n        try {\r\n          await authApi.logout();\r\n        } catch (error) {\r\n          // Continue with logout even if API call fails\r\n          console.error('Logout API error:', error);\r\n        } finally {\r\n          // Clear all auth data\r\n          localStorage.removeItem('auth_token');\r\n          localStorage.removeItem('refresh_token');\r\n          localStorage.removeItem('tenant_slug');\r\n          \r\n          set({\r\n            user: null,\r\n            client: null,\r\n            token: null,\r\n            isAuthenticated: false,\r\n            isLoading: false,\r\n          });\r\n        }\r\n      },\r\n\r\n      setUser: (user: User) => {\r\n        set({ user });\r\n      },\r\n\r\n      setClient: (client: Client) => {\r\n        set({ client });\r\n        localStorage.setItem('tenant_slug', client.slug);\r\n      },\r\n\r\n      setToken: (token: string) => {\r\n        set({ token, isAuthenticated: true });\r\n        localStorage.setItem('auth_token', token);\r\n      },\r\n\r\n      clearAuth: () => {\r\n        localStorage.removeItem('auth_token');\r\n        localStorage.removeItem('refresh_token');\r\n        localStorage.removeItem('tenant_slug');\r\n        \r\n        set({\r\n          user: null,\r\n          client: null,\r\n          token: null,\r\n          isAuthenticated: false,\r\n          isLoading: false,\r\n        });\r\n      },\r\n\r\n      validateTenant: async (slug: string): Promise<boolean> => {\r\n        try {\r\n          const response = await authApi.validateTenant(slug);\r\n          if (response.valid && response.client) {\r\n            set({ client: response.client });\r\n            localStorage.setItem('tenant_slug', slug);\r\n            return true;\r\n          }\r\n          return false;\r\n        } catch (error) {\r\n          console.error('Tenant validation error:', error);\r\n          return false;\r\n        }\r\n      },\r\n\r\n      initializeAuth: () => {\r\n        const token = localStorage.getItem('auth_token');\r\n        const tenantSlug = localStorage.getItem('tenant_slug');\r\n        \r\n        if (token) {\r\n          set({ \r\n            token, \r\n            isAuthenticated: true,\r\n            isLoading: false \r\n          });\r\n          \r\n          // If we have a tenant slug, validate it\r\n          if (tenantSlug) {\r\n            get().validateTenant(tenantSlug);\r\n          }\r\n        } else {\r\n          set({ isLoading: false });\r\n        }\r\n      },\r\n    }),\r\n    {\r\n      name: 'auth-storage',\r\n      partialize: (state) => ({\r\n        user: state.user,\r\n        client: state.client,\r\n        isAuthenticated: state.isAuthenticated,\r\n      }),\r\n    }\r\n  )\r\n);"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAeO,MAAM,eAAe,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,4PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,QAAQ;QACR,OAAO;QACP,iBAAiB;QACjB,WAAW;QAEX,UAAU;QACV,OAAO,OAAO;YACZ,IAAI;gBACF,IAAI;oBAAE,WAAW;gBAAK;gBAEtB,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACrC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG;gBAE9C,eAAe;gBACf,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,iBAAiB;gBACtC,aAAa,OAAO,CAAC,eAAe,OAAO,IAAI;gBAE/C,IAAI;oBACF;oBACA;oBACA;oBACA,iBAAiB;oBACjB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;YACvB;QACF;QAEA,UAAU,OAAO;YACf,IAAI;gBACF,IAAI;oBAAE,WAAW;gBAAK;gBAEtB,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;gBACxC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG;gBAE9C,eAAe;gBACf,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,iBAAiB;gBACtC,aAAa,OAAO,CAAC,eAAe,OAAO,IAAI;gBAE/C,IAAI;oBACF;oBACA;oBACA;oBACA,iBAAiB;oBACjB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;YACvB;QACF;QAEA,QAAQ;YACN,IAAI;gBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,8CAA8C;gBAC9C,QAAQ,KAAK,CAAC,qBAAqB;YACrC,SAAU;gBACR,sBAAsB;gBACtB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,IAAI;oBACF,MAAM;oBACN,QAAQ;oBACR,OAAO;oBACP,iBAAiB;oBACjB,WAAW;gBACb;YACF;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;QACb;QAEA,WAAW,CAAC;YACV,IAAI;gBAAE;YAAO;YACb,aAAa,OAAO,CAAC,eAAe,OAAO,IAAI;QACjD;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;gBAAO,iBAAiB;YAAK;YACnC,aAAa,OAAO,CAAC,cAAc;QACrC;QAEA,WAAW;YACT,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,IAAI;gBACF,MAAM;gBACN,QAAQ;gBACR,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAC9C,IAAI,SAAS,KAAK,IAAI,SAAS,MAAM,EAAE;oBACrC,IAAI;wBAAE,QAAQ,SAAS,MAAM;oBAAC;oBAC9B,aAAa,OAAO,CAAC,eAAe;oBACpC,OAAO;gBACT;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;YACT;QACF;QAEA,gBAAgB;YACd,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,OAAO;gBACT,IAAI;oBACF;oBACA,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,wCAAwC;gBACxC,IAAI,YAAY;oBACd,MAAM,cAAc,CAAC;gBACvB;YACF,OAAO;gBACL,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM;YACpB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,4TAAC,kRAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,4TAAC,kRAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,4TAAC,kRAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,4TAAC,kRAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,4TAAC,kRAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,4TAAC;;0BACC,4TAAC;;;;;0BACD,4TAAC,kRAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,4TAAC,kRAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,4TAAC,uRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,4TAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,4TAAC,kRAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,4TAAC,kRAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { useAuthStore } from \"@/store/auth\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport {\r\n  BarChart3,\r\n  Brain,\r\n  Home,\r\n  LogOut,\r\n  Menu,\r\n  Search,\r\n  Settings,\r\n  TrendingUp,\r\n  User,\r\n} from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface DashboardLayoutProps {\r\n  children: React.ReactNode;\r\n  tenantSlug: string;\r\n}\r\n\r\nconst navigation = [\r\n  { name: \"Dashboard\", href: \"/dashboard\", icon: Home },\r\n  { name: \"Keywords\", href: \"/keywords\", icon: Search },\r\n  { name: \"Analytics\", href: \"/analytics\", icon: BarChart3 },\r\n  { name: \"Performance\", href: \"/performance\", icon: TrendingUp },\r\n  { name: \"AI Insights\", href: \"/insights\", icon: Brain },\r\n  { name: \"Settings\", href: \"/settings\", icon: Settings },\r\n];\r\n\r\nexport function DashboardLayout({\r\n  children,\r\n  tenantSlug,\r\n}: DashboardLayoutProps) {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, client, logout } = useAuthStore();\r\n\r\n  const handleLogout = async () => {\r\n    await logout();\r\n    router.push(`/${tenantSlug}/login`);\r\n  };\r\n\r\n  const isActivePath = (href: string) => {\r\n    const fullPath = `/${tenantSlug}${href}`;\r\n    return pathname === fullPath;\r\n  };\r\n\r\n  const NavigationItems = ({ mobile = false }: { mobile?: boolean }) => (\r\n    <nav className={cn(\"space-y-1\", mobile && \"px-2 pt-2 pb-3\")}>\r\n      {navigation.map((item) => {\r\n        const Icon = item.icon;\r\n        const isActive = isActivePath(item.href);\r\n\r\n        return (\r\n          <Link\r\n            key={item.name}\r\n            href={`/${tenantSlug}${item.href}`}\r\n            className={cn(\r\n              \"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors\",\r\n              isActive\r\n                ? \"bg-blue-100 text-blue-900\"\r\n                : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\r\n            )}\r\n            onClick={() => mobile && setSidebarOpen(false)}\r\n          >\r\n            <Icon\r\n              className={cn(\r\n                \"mr-3 h-5 w-5 flex-shrink-0\",\r\n                isActive\r\n                  ? \"text-blue-500\"\r\n                  : \"text-gray-400 group-hover:text-gray-500\"\r\n              )}\r\n            />\r\n            {item.name}\r\n          </Link>\r\n        );\r\n      })}\r\n    </nav>\r\n  );\r\n\r\n  return (\r\n    <div className='flex h-screen bg-gray-50'>\r\n      {/* Desktop sidebar */}\r\n      <div className='hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:top-16'>\r\n        <div className='flex min-h-0 flex-1 flex-col border-r border-gray-200 bg-white'>\r\n          <div className='flex flex-1 flex-col overflow-y-auto pt-5 pb-4'>\r\n            <div className='flex-1 px-3'>\r\n              <NavigationItems />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile sidebar */}\r\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\r\n        <SheetContent side='left' className='w-64 p-0'>\r\n          <div className='flex h-16 items-center px-4 border-b'>\r\n            <h1 className='text-xl font-semibold text-gray-900'>\r\n              {client?.name || tenantSlug}\r\n            </h1>\r\n          </div>\r\n          <div className='mt-5'>\r\n            <NavigationItems mobile />\r\n          </div>\r\n        </SheetContent>\r\n\r\n        {/* Main content */}\r\n        <div className='md:pl-64 pt-16'>\r\n          {/* Mobile menu button */}\r\n          <div className='md:hidden fixed top-4 left-4 z-20'>\r\n            <SheetTrigger asChild>\r\n              <Button\r\n                variant='ghost'\r\n                size='sm'\r\n                className='px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500'\r\n              >\r\n                <Menu className='h-6 w-6' />\r\n              </Button>\r\n            </SheetTrigger>\r\n          </div>\r\n\r\n          {/* Page content */}\r\n          <main className='flex-1'>\r\n            <div className='py-6 px-4 sm:px-6 lg:px-8'>{children}</div>\r\n          </main>\r\n        </div>\r\n      </Sheet>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AA3BA;;;;;;;;;AAkCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0RAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6RAAA,CAAA,SAAM;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,ySAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,ySAAA,CAAA,aAAU;IAAC;IAC9D;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,2RAAA,CAAA,QAAK;IAAC;IACtD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,iSAAA,CAAA,WAAQ;IAAC;CACvD;AAEM,SAAS,gBAAgB,KAGT;QAHS,EAC9B,QAAQ,EACR,UAAU,EACW,GAHS;;IAI9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAE5C,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC,AAAC,IAAc,OAAX,YAAW;IAC7B;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,AAAC,IAAgB,OAAb,YAAkB,OAAL;QAClC,OAAO,aAAa;IACtB;IAEA,MAAM,kBAAkB;YAAC,EAAE,SAAS,KAAK,EAAwB;6BAC/D,4TAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,UAAU;sBACvC,WAAW,GAAG,CAAC,CAAC;gBACf,MAAM,OAAO,KAAK,IAAI;gBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gBAEvC,qBACE,4TAAC,8RAAA,CAAA,UAAI;oBAEH,MAAM,AAAC,IAAgB,OAAb,YAAuB,OAAV,KAAK,IAAI;oBAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,8BACA;oBAEN,SAAS,IAAM,UAAU,eAAe;;sCAExC,4TAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WACI,kBACA;;;;;;wBAGP,KAAK,IAAI;;mBAlBL,KAAK,IAAI;;;;;YAqBpB;;;;;;;IAIJ,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAOT,4TAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;;kCACtC,4TAAC,oIAAA,CAAA,eAAY;wBAAC,MAAK;wBAAO,WAAU;;0CAClC,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAG,WAAU;8CACX,CAAA,mBAAA,6BAAA,OAAQ,IAAI,KAAI;;;;;;;;;;;0CAGrB,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAgB,MAAM;;;;;;;;;;;;;;;;;kCAK3B,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,oIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;0CAMtB,4TAAC;gCAAK,WAAU;0CACd,cAAA,4TAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;GArGgB;;QAKG,oQAAA,CAAA,cAAW;QACb,oQAAA,CAAA,YAAS;QACS,uHAAA,CAAA,eAAY;;;KAP/B", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/api/tenant-client.ts"], "sourcesContent": ["// lib/api/tenant-client.ts - Enterprise HTTP-only cookie architecture\r\nimport { AUTH_CONFIG } from '../auth-config';\r\nimport { generateCorrelationId } from '../utils/correlation';\r\n\r\n/**\r\n * Custom error classes for enterprise error handling\r\n */\r\nexport class TenantAccessError extends <PERSON>rror {\r\n  constructor(tenantSlug: string) {\r\n    super(`Access denied for tenant: ${tenantSlug}`);\r\n    this.name = 'TenantAccessError';\r\n  }\r\n}\r\n\r\nexport class APIError extends Error {\r\n  constructor(public statusCode: number, message: string) {\r\n    super(message);\r\n    this.name = 'APIError';\r\n  }\r\n}\r\n\r\n/**\r\n * Enterprise tenant-aware API client using HTTP-only cookies\r\n * Eliminates localStorage security vulnerabilities\r\n */\r\nexport async function fetchWithTenant<T>(\r\n  tenantSlug: string,\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<T> {\r\n  const correlationId = generateCorrelationId();\r\n  const url = `${AUTH_CONFIG.API_BASE_URL}/api/${tenantSlug}${endpoint}`;\r\n\r\n  const config: RequestInit = {\r\n    ...options,\r\n    credentials: 'include', // ✅ HTTP-only cookies only\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'X-Correlation-ID': correlationId,\r\n      'X-Tenant-Slug': tenantSlug, // Explicit tenant context\r\n      ...options.headers,\r\n    },\r\n  };\r\n\r\n  try {\r\n    const response = await fetch(url, config);\r\n\r\n    if (!response.ok) {\r\n      await handleAPIError(response, tenantSlug, correlationId);\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`[${correlationId}] API Error for tenant ${tenantSlug}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Centralized error handling with enterprise logging\r\n */\r\nasync function handleAPIError(\r\n  response: Response,\r\n  tenantSlug: string,\r\n  correlationId: string\r\n): Promise<never> {\r\n  const errorData = await response.json().catch(() => ({}));\r\n\r\n  switch (response.status) {\r\n    case 401:\r\n      // Authentication failed - redirect to login\r\n      if (typeof window !== 'undefined') {\r\n        window.location.href = `/login?redirect=/${tenantSlug}&error=auth_required`;\r\n      }\r\n      throw new Error('Authentication required');\r\n\r\n    case 403:\r\n      // Tenant access denied\r\n      console.error(`[${correlationId}] Tenant access denied:`, {\r\n        tenantSlug,\r\n        status: response.status,\r\n        url: response.url\r\n      });\r\n      throw new TenantAccessError(tenantSlug);\r\n\r\n    case 404:\r\n      throw new APIError(404, errorData.message || 'Resource not found');\r\n\r\n    case 500:\r\n      console.error(`[${correlationId}] Server error:`, {\r\n        tenantSlug,\r\n        error: errorData,\r\n        url: response.url\r\n      });\r\n      throw new APIError(500, 'Internal server error');\r\n\r\n    default:\r\n      throw new APIError(\r\n        response.status, \r\n        errorData.message || `HTTP error! status: ${response.status}`\r\n      );\r\n  }\r\n}\r\n\r\n/**\r\n * Onboarding step submission with enhanced error handling\r\n */\r\nexport async function submitOnboardingStep(\r\n  tenantSlug: string,\r\n  step: number,\r\n  data: Record<string, unknown>\r\n): Promise<void> {\r\n  try {\r\n    await fetchWithTenant(\r\n      tenantSlug,\r\n      '/onboarding/',\r\n      {\r\n        method: 'POST',\r\n        body: JSON.stringify({\r\n          step,\r\n          data,\r\n          timestamp: new Date().toISOString(),\r\n        }),\r\n      }\r\n    );\r\n  } catch (error) {\r\n    if (error instanceof TenantAccessError) {\r\n      throw new Error(`Onboarding access denied for tenant: ${tenantSlug}`);\r\n    }\r\n    if (error instanceof APIError) {\r\n      throw new Error(`Failed to save step ${step}: ${error.message}`);\r\n    }\r\n    throw new Error(`Failed to save onboarding step ${step}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Get onboarding status with type safety\r\n */\r\nexport async function getOnboardingStatus(tenantSlug: string): Promise<{\r\n  currentStep: number;\r\n  completedSteps: number[];\r\n  isComplete: boolean;\r\n  businessIntelligence?: {\r\n    completed: boolean;\r\n    data?: Record<string, unknown>;\r\n  };\r\n  competitiveIntelligence?: {\r\n    completed: boolean;\r\n    data?: Record<string, unknown>;\r\n  };\r\n  marketingIntelligence?: {\r\n    completed: boolean;\r\n    data?: Record<string, unknown>;\r\n  };\r\n}> {\r\n  return fetchWithTenant(tenantSlug, '/onboarding/status/');\r\n}\r\n\r\n/**\r\n * Reset onboarding (admin/testing only)\r\n */\r\nexport async function resetOnboarding(tenantSlug: string): Promise<void> {\r\n  await fetchWithTenant(tenantSlug, '/onboarding/reset/', {\r\n    method: 'POST',\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;;;;;;AACtE;AACA;;;;AAKO,MAAM,0BAA0B;IACrC,YAAY,UAAkB,CAAE;QAC9B,KAAK,CAAC,AAAC,6BAAuC,OAAX;QACnC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,iBAAiB;IAC5B,YAAY,AAAO,UAAkB,EAAE,OAAe,CAAE;QACtD,KAAK,CAAC,mRADW,aAAA;QAEjB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAMO,eAAe,gBACpB,UAAkB,EAClB,QAAgB;QAChB,UAAA,iEAAuB,CAAC;IAExB,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;IAC1C,MAAM,MAAM,AAAC,GAAkC,OAAhC,+HAAA,CAAA,cAAW,CAAC,YAAY,EAAC,SAAoB,OAAb,YAAsB,OAAT;IAE5D,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,aAAa;QACb,SAAS;YACP,gBAAgB;YAChB,oBAAoB;YACpB,iBAAiB;YACjB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,eAAe,UAAU,YAAY;QAC7C;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,AAAC,IAA0C,OAAvC,eAAc,2BAAoC,OAAX,YAAW,MAAI;QACxE,MAAM;IACR;AACF;AAEA;;CAEC,GACD,eAAe,eACb,QAAkB,EAClB,UAAkB,EAClB,aAAqB;IAErB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;IAEvD,OAAQ,SAAS,MAAM;QACrB,KAAK;YACH,4CAA4C;YAC5C,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,oBAA8B,OAAX,YAAW;YACxD;YACA,MAAM,IAAI,MAAM;QAElB,KAAK;YACH,uBAAuB;YACvB,QAAQ,KAAK,CAAC,AAAC,IAAiB,OAAd,eAAc,4BAA0B;gBACxD;gBACA,QAAQ,SAAS,MAAM;gBACvB,KAAK,SAAS,GAAG;YACnB;YACA,MAAM,IAAI,kBAAkB;QAE9B,KAAK;YACH,MAAM,IAAI,SAAS,KAAK,UAAU,OAAO,IAAI;QAE/C,KAAK;YACH,QAAQ,KAAK,CAAC,AAAC,IAAiB,OAAd,eAAc,oBAAkB;gBAChD;gBACA,OAAO;gBACP,KAAK,SAAS,GAAG;YACnB;YACA,MAAM,IAAI,SAAS,KAAK;QAE1B;YACE,MAAM,IAAI,SACR,SAAS,MAAM,EACf,UAAU,OAAO,IAAI,AAAC,uBAAsC,OAAhB,SAAS,MAAM;IAEjE;AACF;AAKO,eAAe,qBACpB,UAAkB,EAClB,IAAY,EACZ,IAA6B;IAE7B,IAAI;QACF,MAAM,gBACJ,YACA,gBACA;YACE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IAEJ,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mBAAmB;YACtC,MAAM,IAAI,MAAM,AAAC,wCAAkD,OAAX;QAC1D;QACA,IAAI,iBAAiB,UAAU;YAC7B,MAAM,IAAI,MAAM,AAAC,uBAA+B,OAAT,MAAK,MAAkB,OAAd,MAAM,OAAO;QAC/D;QACA,MAAM,IAAI,MAAM,AAAC,kCAA0C,OAAT,MAAK,MAA6D,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IACtG;AACF;AAKO,eAAe,oBAAoB,UAAkB;IAiB1D,OAAO,gBAAgB,YAAY;AACrC;AAKO,eAAe,gBAAgB,UAAkB;IACtD,MAAM,gBAAgB,YAAY,sBAAsB;QACtD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/api/changelog.ts"], "sourcesContent": ["/**\n * Changelog API Client - Business-friendly changelog system integration.\n * \n * Following established tenant-aware patterns with proper error handling\n * and type safety for changelog operations.\n */\n\nimport { fetchWithTenant } from './tenant-client';\n\n// Type definitions matching Django serializers\nexport interface ChangelogTag {\n  id: number;\n  name: string;\n  color: string;\n  description: string;\n}\n\nexport interface ChangelogMedia {\n  id: number;\n  media_type: 'image' | 'gif' | 'video';\n  file: string;\n  alt_text: string;\n  caption: string;\n  order: number;\n  created_at: string;\n}\n\nexport interface ChangelogEntryList {\n  id: number;\n  title: string;\n  business_benefit: string;\n  action_required: string;\n  entry_type: 'new' | 'improvement' | 'bugfix' | 'announcement';\n  entry_type_display: string;\n  impact_level: 'high' | 'medium' | 'low';\n  impact_level_display: string;\n  revenue_impact: string;\n  social_proof: string;\n  competitive_advantage: string;\n  is_featured: boolean;\n  published_date: string;\n  author_name: string;\n  tags: ChangelogTag[];\n  media_count: number;\n  feedback_summary: {\n    total: number;\n    helpful: number;\n    not_helpful: number;\n  };\n}\n\nexport interface ChangelogEntryDetail extends ChangelogEntryList {\n  media: ChangelogMedia[];\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface ChangelogFeedback {\n  id: number;\n  entry: number;\n  feedback_type: 'helpful' | 'not_helpful' | 'suggestion';\n  comment: string;\n  user_name: string;\n  created_at: string;\n}\n\nexport interface ChangelogStats {\n  total_entries: number;\n  featured_entries: number;\n  by_type: Record<string, { count: number; display_name: string }>;\n  by_impact: Record<string, { count: number; display_name: string }>;\n  recent_count: number;\n}\n\nexport interface ChangelogFilters {\n  entry_type?: string;\n  impact_level?: string;\n  tags?: string;\n  featured_only?: boolean;\n}\n\n/**\n * Get paginated list of changelog entries with optional filtering.\n */\nexport async function getChangelogEntries(\n  tenantSlug: string,\n  filters: ChangelogFilters = {},\n  page: number = 1\n): Promise<{\n  results: ChangelogEntryList[];\n  count: number;\n  next: string | null;\n  previous: string | null;\n}> {\n  const params = new URLSearchParams();\n  \n  if (filters.entry_type) params.append('entry_type', filters.entry_type);\n  if (filters.impact_level) params.append('impact_level', filters.impact_level);\n  if (filters.tags) params.append('tags', filters.tags);\n  if (filters.featured_only) params.append('featured_only', 'true');\n  if (page > 1) params.append('page', page.toString());\n  \n  const queryString = params.toString();\n  const endpoint = `/changelog/entries/${queryString ? `?${queryString}` : ''}`;\n  \n  return fetchWithTenant(tenantSlug, endpoint);\n}\n\n/**\n * Get detailed information for a specific changelog entry.\n */\nexport async function getChangelogEntry(\n  tenantSlug: string,\n  entryId: number\n): Promise<ChangelogEntryDetail> {\n  return fetchWithTenant(tenantSlug, `/changelog/entries/${entryId}/`);\n}\n\n/**\n * Get changelog statistics for the current tenant.\n */\nexport async function getChangelogStats(\n  tenantSlug: string\n): Promise<ChangelogStats> {\n  return fetchWithTenant(tenantSlug, '/changelog/entries/stats/');\n}\n\n/**\n * Get all available changelog tags for filtering.\n */\nexport async function getChangelogTags(\n  tenantSlug: string\n): Promise<ChangelogTag[]> {\n  const response = await fetchWithTenant<{ results: ChangelogTag[] }>(\n    tenantSlug, \n    '/changelog/tags/'\n  );\n  return response.results;\n}\n\n/**\n * Submit feedback for a changelog entry.\n */\nexport async function submitChangelogFeedback(\n  tenantSlug: string,\n  entryId: number,\n  feedback: {\n    feedback_type: 'helpful' | 'not_helpful' | 'suggestion';\n    comment?: string;\n  }\n): Promise<ChangelogFeedback> {\n  return fetchWithTenant(tenantSlug, `/changelog/entries/${entryId}/feedback/`, {\n    method: 'POST',\n    body: JSON.stringify(feedback),\n  });\n}\n\n/**\n * Helper function to get the emoji for an entry type.\n */\nexport function getEntryTypeEmoji(entryType: string): string {\n  const emojiMap: Record<string, string> = {\n    'new': '🆕',\n    'improvement': '⚡',\n    'bugfix': '🐛',\n    'announcement': '📢',\n  };\n  return emojiMap[entryType] || '📝';\n}\n\n/**\n * Helper function to get the color class for impact level.\n */\nexport function getImpactLevelColor(impactLevel: string): string {\n  const colorMap: Record<string, string> = {\n    'high': 'bg-red-100 text-red-800 border-red-200',\n    'medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    'low': 'bg-green-100 text-green-800 border-green-200',\n  };\n  return colorMap[impactLevel] || 'bg-gray-100 text-gray-800 border-gray-200';\n}\n\n/**\n * Helper function to format published date in a user-friendly way.\n */\nexport function formatPublishedDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) {\n    return 'Today';\n  } else if (diffInDays === 1) {\n    return 'Yesterday';\n  } else if (diffInDays < 7) {\n    return `${diffInDays} days ago`;\n  } else if (diffInDays < 30) {\n    const weeks = Math.floor(diffInDays / 7);\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;\n  } else {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;AAED;;AA6EO,eAAe,oBACpB,UAAkB;QAClB,UAAA,iEAA4B,CAAC,GAC7B,OAAA,iEAAe;IAOf,MAAM,SAAS,IAAI;IAEnB,IAAI,QAAQ,UAAU,EAAE,OAAO,MAAM,CAAC,cAAc,QAAQ,UAAU;IACtE,IAAI,QAAQ,YAAY,EAAE,OAAO,MAAM,CAAC,gBAAgB,QAAQ,YAAY;IAC5E,IAAI,QAAQ,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;IACpD,IAAI,QAAQ,aAAa,EAAE,OAAO,MAAM,CAAC,iBAAiB;IAC1D,IAAI,OAAO,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAEjD,MAAM,cAAc,OAAO,QAAQ;IACnC,MAAM,WAAW,AAAC,sBAA0D,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;IAEzE,OAAO,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;AACrC;AAKO,eAAe,kBACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,AAAC,sBAA6B,OAAR,SAAQ;AACnE;AAKO,eAAe,kBACpB,UAAkB;IAElB,OAAO,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;AACrC;AAKO,eAAe,iBACpB,UAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EACnC,YACA;IAEF,OAAO,SAAS,OAAO;AACzB;AAKO,eAAe,wBACpB,UAAkB,EAClB,OAAe,EACf,QAGC;IAED,OAAO,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,AAAC,sBAA6B,OAAR,SAAQ,eAAa;QAC5E,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,WAAmC;QACvC,OAAO;QACP,eAAe;QACf,UAAU;QACV,gBAAgB;IAClB;IACA,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC;AAKO,SAAS,oBAAoB,WAAmB;IACrD,MAAM,WAAmC;QACvC,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,OAAO,QAAQ,CAAC,YAAY,IAAI;AAClC;AAKO,SAAS,oBAAoB,UAAkB;IACpD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,AAAC,GAAa,OAAX,YAAW;IACvB,OAAO,IAAI,aAAa,IAAI;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;IAC9C,OAAO;QACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;AACF", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/features/changelog/components/client/changelog-filters.tsx"], "sourcesContent": ["'use client';\n\n/**\n * Changelog Filters - Interactive filtering for changelog entries.\n * \n * Client component that provides filtering options for entry type,\n * impact level, tags, and featured status.\n */\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { getChangelogTags, ChangelogTag } from '@/lib/api/changelog';\n\ninterface ChangelogFiltersProps {\n  tenantSlug: string;\n  currentFilters: {\n    entry_type?: string;\n    impact_level?: string;\n    tags?: string;\n    featured_only?: string;\n  };\n}\n\nconst ENTRY_TYPES = [\n  { value: 'new', label: '🆕 New Features', color: 'bg-green-100 text-green-800' },\n  { value: 'improvement', label: '⚡ Improvements', color: 'bg-blue-100 text-blue-800' },\n  { value: 'bugfix', label: '🐛 Bug Fixes', color: 'bg-yellow-100 text-yellow-800' },\n  { value: 'announcement', label: '📢 Announcements', color: 'bg-purple-100 text-purple-800' },\n];\n\nconst IMPACT_LEVELS = [\n  { value: 'high', label: 'High Impact', color: 'bg-red-100 text-red-800' },\n  { value: 'medium', label: 'Medium Impact', color: 'bg-yellow-100 text-yellow-800' },\n  { value: 'low', label: 'Low Impact', color: 'bg-green-100 text-green-800' },\n];\n\nexport function ChangelogFilters({ tenantSlug, currentFilters }: ChangelogFiltersProps) {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const [tags, setTags] = useState<ChangelogTag[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    async function loadTags() {\n      try {\n        const tagData = await getChangelogTags(tenantSlug);\n        setTags(tagData);\n      } catch (error) {\n        console.error('Failed to load tags:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    }\n\n    loadTags();\n  }, [tenantSlug]);\n\n  const updateFilter = (key: string, value: string | null) => {\n    const params = new URLSearchParams(searchParams);\n    \n    if (value) {\n      params.set(key, value);\n    } else {\n      params.delete(key);\n    }\n    \n    // Reset to first page when filtering\n    params.delete('page');\n    \n    const queryString = params.toString();\n    const newUrl = `/${tenantSlug}/changelog${queryString ? `?${queryString}` : ''}`;\n    \n    router.push(newUrl);\n  };\n\n  const clearAllFilters = () => {\n    router.push(`/${tenantSlug}/changelog`);\n  };\n\n  const hasActiveFilters = Object.values(currentFilters).some(value => value);\n\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Filter Updates</h2>\n        {hasActiveFilters && (\n          <button\n            onClick={clearAllFilters}\n            className=\"text-sm text-blue-600 hover:text-blue-800 font-medium\"\n          >\n            Clear all filters\n          </button>\n        )}\n      </div>\n      \n      <div className=\"space-y-6\">\n        {/* Entry Type Filter */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n            Update Type\n          </label>\n          <div className=\"flex flex-wrap gap-2\">\n            {ENTRY_TYPES.map((type) => (\n              <button\n                key={type.value}\n                onClick={() => updateFilter('entry_type', \n                  currentFilters.entry_type === type.value ? null : type.value\n                )}\n                className={`px-3 py-2 rounded-full text-sm font-medium border transition-colors ${\n                  currentFilters.entry_type === type.value\n                    ? `${type.color} border-current`\n                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'\n                }`}\n              >\n                {type.label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Impact Level Filter */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n            Business Impact\n          </label>\n          <div className=\"flex flex-wrap gap-2\">\n            {IMPACT_LEVELS.map((level) => (\n              <button\n                key={level.value}\n                onClick={() => updateFilter('impact_level', \n                  currentFilters.impact_level === level.value ? null : level.value\n                )}\n                className={`px-3 py-2 rounded-full text-sm font-medium border transition-colors ${\n                  currentFilters.impact_level === level.value\n                    ? `${level.color} border-current`\n                    : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'\n                }`}\n              >\n                {level.label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Tags Filter */}\n        {!isLoading && tags.length > 0 && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n              Categories\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {tags.map((tag) => {\n                const isActive = currentFilters.tags?.split(',').includes(tag.name);\n                return (\n                  <button\n                    key={tag.id}\n                    onClick={() => {\n                      const currentTags = currentFilters.tags?.split(',').filter(Boolean) || [];\n                      let newTags;\n                      \n                      if (isActive) {\n                        newTags = currentTags.filter(t => t !== tag.name);\n                      } else {\n                        newTags = [...currentTags, tag.name];\n                      }\n                      \n                      updateFilter('tags', newTags.length > 0 ? newTags.join(',') : null);\n                    }}\n                    className={`px-3 py-2 rounded-full text-sm font-medium border transition-colors ${\n                      isActive\n                        ? 'border-current'\n                        : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'\n                    }`}\n                    style={isActive ? {\n                      backgroundColor: `${tag.color}20`,\n                      color: tag.color,\n                      borderColor: `${tag.color}60`\n                    } : {}}\n                  >\n                    {tag.name}\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {/* Featured Only Toggle */}\n        <div>\n          <label className=\"flex items-center space-x-3\">\n            <input\n              type=\"checkbox\"\n              checked={currentFilters.featured_only === 'true'}\n              onChange={(e) => updateFilter('featured_only', e.target.checked ? 'true' : null)}\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <span className=\"text-sm font-medium text-gray-700\">\n              Show only featured updates ⭐\n            </span>\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;CAKC,GAED;AACA;AACA;;;AAXA;;;;AAuBA,MAAM,cAAc;IAClB;QAAE,OAAO;QAAO,OAAO;QAAmB,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAe,OAAO;QAAkB,OAAO;IAA4B;IACpF;QAAE,OAAO;QAAU,OAAO;QAAgB,OAAO;IAAgC;IACjF;QAAE,OAAO;QAAgB,OAAO;QAAoB,OAAO;IAAgC;CAC5F;AAED,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAQ,OAAO;QAAe,OAAO;IAA0B;IACxE;QAAE,OAAO;QAAU,OAAO;QAAiB,OAAO;IAAgC;IAClF;QAAE,OAAO;QAAO,OAAO;QAAc,OAAO;IAA8B;CAC3E;AAEM,SAAS,iBAAiB,KAAqD;QAArD,EAAE,UAAU,EAAE,cAAc,EAAyB,GAArD;;IAC/B,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;sCAAE;YACR,eAAe;gBACb,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACvC,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACxC,SAAU;oBACR,aAAa;gBACf;YACF;YAEA;QACF;qCAAG;QAAC;KAAW;IAEf,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,SAAS,IAAI,gBAAgB;QAEnC,IAAI,OAAO;YACT,OAAO,GAAG,CAAC,KAAK;QAClB,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QAEA,qCAAqC;QACrC,OAAO,MAAM,CAAC;QAEd,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,SAAS,AAAC,IAA0B,OAAvB,YAAW,cAAiD,OAArC,cAAc,AAAC,IAAe,OAAZ,eAAgB;QAE5E,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,AAAC,IAAc,OAAX,YAAW;IAC7B;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAA,QAAS;IAErE,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCAAsC;;;;;;oBACnD,kCACC,4TAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAML,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;;0CACC,4TAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4TAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,4TAAC;wCAEC,SAAS,IAAM,aAAa,cAC1B,eAAe,UAAU,KAAK,KAAK,KAAK,GAAG,OAAO,KAAK,KAAK;wCAE9D,WAAW,AAAC,uEAIX,OAHC,eAAe,UAAU,KAAK,KAAK,KAAK,GACpC,AAAC,GAAa,OAAX,KAAK,KAAK,EAAC,qBACd;kDAGL,KAAK,KAAK;uCAVN,KAAK,KAAK;;;;;;;;;;;;;;;;kCAiBvB,4TAAC;;0CACC,4TAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4TAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,4TAAC;wCAEC,SAAS,IAAM,aAAa,gBAC1B,eAAe,YAAY,KAAK,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK;wCAElE,WAAW,AAAC,uEAIX,OAHC,eAAe,YAAY,KAAK,MAAM,KAAK,GACvC,AAAC,GAAc,OAAZ,MAAM,KAAK,EAAC,qBACf;kDAGL,MAAM,KAAK;uCAVP,MAAM,KAAK;;;;;;;;;;;;;;;;oBAiBvB,CAAC,aAAa,KAAK,MAAM,GAAG,mBAC3B,4TAAC;;0CACC,4TAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4TAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC;wCACQ;oCAAjB,MAAM,YAAW,uBAAA,eAAe,IAAI,cAAnB,2CAAA,qBAAqB,KAAK,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI;oCAClE,qBACE,4TAAC;wCAEC,SAAS;gDACa;4CAApB,MAAM,cAAc,EAAA,uBAAA,eAAe,IAAI,cAAnB,2CAAA,qBAAqB,KAAK,CAAC,KAAK,MAAM,CAAC,aAAY,EAAE;4CACzE,IAAI;4CAEJ,IAAI,UAAU;gDACZ,UAAU,YAAY,MAAM,CAAC,CAAA,IAAK,MAAM,IAAI,IAAI;4CAClD,OAAO;gDACL,UAAU;uDAAI;oDAAa,IAAI,IAAI;iDAAC;4CACtC;4CAEA,aAAa,QAAQ,QAAQ,MAAM,GAAG,IAAI,QAAQ,IAAI,CAAC,OAAO;wCAChE;wCACA,WAAW,AAAC,uEAIX,OAHC,WACI,mBACA;wCAEN,OAAO,WAAW;4CAChB,iBAAiB,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;4CAC9B,OAAO,IAAI,KAAK;4CAChB,aAAa,AAAC,GAAY,OAAV,IAAI,KAAK,EAAC;wCAC5B,IAAI,CAAC;kDAEJ,IAAI,IAAI;uCAxBJ,IAAI,EAAE;;;;;gCA2BjB;;;;;;;;;;;;kCAMN,4TAAC;kCACC,cAAA,4TAAC;4BAAM,WAAU;;8CACf,4TAAC;oCACC,MAAK;oCACL,SAAS,eAAe,aAAa,KAAK;oCAC1C,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,OAAO,GAAG,SAAS;oCAC3E,WAAU;;;;;;8CAEZ,4TAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhE;GAxKgB;;QACC,oQAAA,CAAA,YAAS;QACH,oQAAA,CAAA,kBAAe;;;KAFtB", "debugId": null}}]}