{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/dashboard/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/dashboard-layout.tsx <module evaluation>\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+EACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/dashboard/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/dashboard-layout.tsx\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2DACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/utils/correlation.ts"], "sourcesContent": ["// lib/utils/correlation.ts - Request tracing utility (Under 25 lines)\nimport { v4 as uuidv4 } from 'uuid';\n\n/**\n * Generates correlation IDs for cross-stack request tracing\n * Essential for debugging multi-tenant API calls\n */\nexport function generateCorrelationId(): string {\n  return `req_${uuidv4().substring(0, 8)}_${Date.now()}`;\n}\n\n/**\n * Extracts correlation ID from response headers\n */\nexport function getCorrelationId(response: Response): string | null {\n  return response.headers.get('X-Correlation-ID');\n}\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;AACtE;;AAMO,SAAS;IACd,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,4NAAA,CAAA,KAAM,AAAD,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI;AACxD;AAKO,SAAS,iBAAiB,QAAkB;IACjD,OAAO,SAAS,OAAO,CAAC,GAAG,CAAC;AAC9B", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/api/tenant-client.ts"], "sourcesContent": ["// lib/api/tenant-client.ts - Enterprise HTTP-only cookie architecture\r\nimport { AUTH_CONFIG } from '../auth-config';\r\nimport { generateCorrelationId } from '../utils/correlation';\r\n\r\n/**\r\n * Custom error classes for enterprise error handling\r\n */\r\nexport class TenantAccessError extends <PERSON>rror {\r\n  constructor(tenantSlug: string) {\r\n    super(`Access denied for tenant: ${tenantSlug}`);\r\n    this.name = 'TenantAccessError';\r\n  }\r\n}\r\n\r\nexport class APIError extends Error {\r\n  constructor(public statusCode: number, message: string) {\r\n    super(message);\r\n    this.name = 'APIError';\r\n  }\r\n}\r\n\r\n/**\r\n * Enterprise tenant-aware API client using HTTP-only cookies\r\n * Eliminates localStorage security vulnerabilities\r\n */\r\nexport async function fetchWithTenant<T>(\r\n  tenantSlug: string,\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<T> {\r\n  const correlationId = generateCorrelationId();\r\n  const url = `${AUTH_CONFIG.API_BASE_URL}/api/${tenantSlug}${endpoint}`;\r\n\r\n  const config: RequestInit = {\r\n    ...options,\r\n    credentials: 'include', // ✅ HTTP-only cookies only\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'X-Correlation-ID': correlationId,\r\n      'X-Tenant-Slug': tenantSlug, // Explicit tenant context\r\n      ...options.headers,\r\n    },\r\n  };\r\n\r\n  try {\r\n    const response = await fetch(url, config);\r\n\r\n    if (!response.ok) {\r\n      await handleAPIError(response, tenantSlug, correlationId);\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error(`[${correlationId}] API Error for tenant ${tenantSlug}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Centralized error handling with enterprise logging\r\n */\r\nasync function handleAPIError(\r\n  response: Response,\r\n  tenantSlug: string,\r\n  correlationId: string\r\n): Promise<never> {\r\n  const errorData = await response.json().catch(() => ({}));\r\n\r\n  switch (response.status) {\r\n    case 401:\r\n      // Authentication failed - redirect to login\r\n      if (typeof window !== 'undefined') {\r\n        window.location.href = `/login?redirect=/${tenantSlug}&error=auth_required`;\r\n      }\r\n      throw new Error('Authentication required');\r\n\r\n    case 403:\r\n      // Tenant access denied\r\n      console.error(`[${correlationId}] Tenant access denied:`, {\r\n        tenantSlug,\r\n        status: response.status,\r\n        url: response.url\r\n      });\r\n      throw new TenantAccessError(tenantSlug);\r\n\r\n    case 404:\r\n      throw new APIError(404, errorData.message || 'Resource not found');\r\n\r\n    case 500:\r\n      console.error(`[${correlationId}] Server error:`, {\r\n        tenantSlug,\r\n        error: errorData,\r\n        url: response.url\r\n      });\r\n      throw new APIError(500, 'Internal server error');\r\n\r\n    default:\r\n      throw new APIError(\r\n        response.status, \r\n        errorData.message || `HTTP error! status: ${response.status}`\r\n      );\r\n  }\r\n}\r\n\r\n/**\r\n * Onboarding step submission with enhanced error handling\r\n */\r\nexport async function submitOnboardingStep(\r\n  tenantSlug: string,\r\n  step: number,\r\n  data: Record<string, unknown>\r\n): Promise<void> {\r\n  try {\r\n    await fetchWithTenant(\r\n      tenantSlug,\r\n      '/onboarding/',\r\n      {\r\n        method: 'POST',\r\n        body: JSON.stringify({\r\n          step,\r\n          data,\r\n          timestamp: new Date().toISOString(),\r\n        }),\r\n      }\r\n    );\r\n  } catch (error) {\r\n    if (error instanceof TenantAccessError) {\r\n      throw new Error(`Onboarding access denied for tenant: ${tenantSlug}`);\r\n    }\r\n    if (error instanceof APIError) {\r\n      throw new Error(`Failed to save step ${step}: ${error.message}`);\r\n    }\r\n    throw new Error(`Failed to save onboarding step ${step}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Get onboarding status with type safety\r\n */\r\nexport async function getOnboardingStatus(tenantSlug: string): Promise<{\r\n  currentStep: number;\r\n  completedSteps: number[];\r\n  isComplete: boolean;\r\n  businessIntelligence?: {\r\n    completed: boolean;\r\n    data?: Record<string, unknown>;\r\n  };\r\n  competitiveIntelligence?: {\r\n    completed: boolean;\r\n    data?: Record<string, unknown>;\r\n  };\r\n  marketingIntelligence?: {\r\n    completed: boolean;\r\n    data?: Record<string, unknown>;\r\n  };\r\n}> {\r\n  return fetchWithTenant(tenantSlug, '/onboarding/status/');\r\n}\r\n\r\n/**\r\n * Reset onboarding (admin/testing only)\r\n */\r\nexport async function resetOnboarding(tenantSlug: string): Promise<void> {\r\n  await fetchWithTenant(tenantSlug, '/onboarding/reset/', {\r\n    method: 'POST',\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;;;;;AACtE;AACA;;;AAKO,MAAM,0BAA0B;IACrC,YAAY,UAAkB,CAAE;QAC9B,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY;QAC/C,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,iBAAiB;;IAC5B,YAAY,AAAO,UAAkB,EAAE,OAAe,CAAE;QACtD,KAAK,CAAC,eADW,aAAA;QAEjB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAMO,eAAe,gBACpB,UAAkB,EAClB,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;IAC1C,MAAM,MAAM,GAAG,4HAAA,CAAA,cAAW,CAAC,YAAY,CAAC,KAAK,EAAE,aAAa,UAAU;IAEtE,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,aAAa;QACb,SAAS;YACP,gBAAgB;YAChB,oBAAoB;YACpB,iBAAiB;YACjB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,eAAe,UAAU,YAAY;QAC7C;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,uBAAuB,EAAE,WAAW,CAAC,CAAC,EAAE;QACxE,MAAM;IACR;AACF;AAEA;;CAEC,GACD,eAAe,eACb,QAAkB,EAClB,UAAkB,EAClB,aAAqB;IAErB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;IAEvD,OAAQ,SAAS,MAAM;QACrB,KAAK;YACH,4CAA4C;YAC5C;;YAGA,MAAM,IAAI,MAAM;QAElB,KAAK;YACH,uBAAuB;YACvB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,uBAAuB,CAAC,EAAE;gBACxD;gBACA,QAAQ,SAAS,MAAM;gBACvB,KAAK,SAAS,GAAG;YACnB;YACA,MAAM,IAAI,kBAAkB;QAE9B,KAAK;YACH,MAAM,IAAI,SAAS,KAAK,UAAU,OAAO,IAAI;QAE/C,KAAK;YACH,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,eAAe,CAAC,EAAE;gBAChD;gBACA,OAAO;gBACP,KAAK,SAAS,GAAG;YACnB;YACA,MAAM,IAAI,SAAS,KAAK;QAE1B;YACE,MAAM,IAAI,SACR,SAAS,MAAM,EACf,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAEnE;AACF;AAKO,eAAe,qBACpB,UAAkB,EAClB,IAAY,EACZ,IAA6B;IAE7B,IAAI;QACF,MAAM,gBACJ,YACA,gBACA;YACE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IAEJ,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,mBAAmB;YACtC,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,YAAY;QACtE;QACA,IAAI,iBAAiB,UAAU;YAC7B,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,KAAK,EAAE,EAAE,MAAM,OAAO,EAAE;QACjE;QACA,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,KAAK,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACvH;AACF;AAKO,eAAe,oBAAoB,UAAkB;IAiB1D,OAAO,gBAAgB,YAAY;AACrC;AAKO,eAAe,gBAAgB,UAAkB;IACtD,MAAM,gBAAgB,YAAY,sBAAsB;QACtD,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/api/changelog.ts"], "sourcesContent": ["/**\n * Changelog API Client - Business-friendly changelog system integration.\n * \n * Following established tenant-aware patterns with proper error handling\n * and type safety for changelog operations.\n */\n\nimport { fetchWithTenant } from './tenant-client';\n\n// Type definitions matching Django serializers\nexport interface ChangelogTag {\n  id: number;\n  name: string;\n  color: string;\n  description: string;\n}\n\nexport interface ChangelogMedia {\n  id: number;\n  media_type: 'image' | 'gif' | 'video';\n  file: string;\n  alt_text: string;\n  caption: string;\n  order: number;\n  created_at: string;\n}\n\nexport interface ChangelogEntryList {\n  id: number;\n  title: string;\n  business_benefit: string;\n  action_required: string;\n  entry_type: 'new' | 'improvement' | 'bugfix' | 'announcement';\n  entry_type_display: string;\n  impact_level: 'high' | 'medium' | 'low';\n  impact_level_display: string;\n  revenue_impact: string;\n  social_proof: string;\n  competitive_advantage: string;\n  is_featured: boolean;\n  published_date: string;\n  author_name: string;\n  tags: ChangelogTag[];\n  media_count: number;\n  feedback_summary: {\n    total: number;\n    helpful: number;\n    not_helpful: number;\n  };\n}\n\nexport interface ChangelogEntryDetail extends ChangelogEntryList {\n  media: ChangelogMedia[];\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface ChangelogFeedback {\n  id: number;\n  entry: number;\n  feedback_type: 'helpful' | 'not_helpful' | 'suggestion';\n  comment: string;\n  user_name: string;\n  created_at: string;\n}\n\nexport interface ChangelogStats {\n  total_entries: number;\n  featured_entries: number;\n  by_type: Record<string, { count: number; display_name: string }>;\n  by_impact: Record<string, { count: number; display_name: string }>;\n  recent_count: number;\n}\n\nexport interface ChangelogFilters {\n  entry_type?: string;\n  impact_level?: string;\n  tags?: string;\n  featured_only?: boolean;\n}\n\n/**\n * Get paginated list of changelog entries with optional filtering.\n */\nexport async function getChangelogEntries(\n  tenantSlug: string,\n  filters: ChangelogFilters = {},\n  page: number = 1\n): Promise<{\n  results: ChangelogEntryList[];\n  count: number;\n  next: string | null;\n  previous: string | null;\n}> {\n  const params = new URLSearchParams();\n  \n  if (filters.entry_type) params.append('entry_type', filters.entry_type);\n  if (filters.impact_level) params.append('impact_level', filters.impact_level);\n  if (filters.tags) params.append('tags', filters.tags);\n  if (filters.featured_only) params.append('featured_only', 'true');\n  if (page > 1) params.append('page', page.toString());\n  \n  const queryString = params.toString();\n  const endpoint = `/changelog/entries/${queryString ? `?${queryString}` : ''}`;\n  \n  return fetchWithTenant(tenantSlug, endpoint);\n}\n\n/**\n * Get detailed information for a specific changelog entry.\n */\nexport async function getChangelogEntry(\n  tenantSlug: string,\n  entryId: number\n): Promise<ChangelogEntryDetail> {\n  return fetchWithTenant(tenantSlug, `/changelog/entries/${entryId}/`);\n}\n\n/**\n * Get changelog statistics for the current tenant.\n */\nexport async function getChangelogStats(\n  tenantSlug: string\n): Promise<ChangelogStats> {\n  return fetchWithTenant(tenantSlug, '/changelog/entries/stats/');\n}\n\n/**\n * Get all available changelog tags for filtering.\n */\nexport async function getChangelogTags(\n  tenantSlug: string\n): Promise<ChangelogTag[]> {\n  const response = await fetchWithTenant<{ results: ChangelogTag[] }>(\n    tenantSlug, \n    '/changelog/tags/'\n  );\n  return response.results;\n}\n\n/**\n * Submit feedback for a changelog entry.\n */\nexport async function submitChangelogFeedback(\n  tenantSlug: string,\n  entryId: number,\n  feedback: {\n    feedback_type: 'helpful' | 'not_helpful' | 'suggestion';\n    comment?: string;\n  }\n): Promise<ChangelogFeedback> {\n  return fetchWithTenant(tenantSlug, `/changelog/entries/${entryId}/feedback/`, {\n    method: 'POST',\n    body: JSON.stringify(feedback),\n  });\n}\n\n/**\n * Helper function to get the emoji for an entry type.\n */\nexport function getEntryTypeEmoji(entryType: string): string {\n  const emojiMap: Record<string, string> = {\n    'new': '🆕',\n    'improvement': '⚡',\n    'bugfix': '🐛',\n    'announcement': '📢',\n  };\n  return emojiMap[entryType] || '📝';\n}\n\n/**\n * Helper function to get the color class for impact level.\n */\nexport function getImpactLevelColor(impactLevel: string): string {\n  const colorMap: Record<string, string> = {\n    'high': 'bg-red-100 text-red-800 border-red-200',\n    'medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    'low': 'bg-green-100 text-green-800 border-green-200',\n  };\n  return colorMap[impactLevel] || 'bg-gray-100 text-gray-800 border-gray-200';\n}\n\n/**\n * Helper function to format published date in a user-friendly way.\n */\nexport function formatPublishedDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) {\n    return 'Today';\n  } else if (diffInDays === 1) {\n    return 'Yesterday';\n  } else if (diffInDays < 7) {\n    return `${diffInDays} days ago`;\n  } else if (diffInDays < 30) {\n    const weeks = Math.floor(diffInDays / 7);\n    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;\n  } else {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;AAED;;AA6EO,eAAe,oBACpB,UAAkB,EAClB,UAA4B,CAAC,CAAC,EAC9B,OAAe,CAAC;IAOhB,MAAM,SAAS,IAAI;IAEnB,IAAI,QAAQ,UAAU,EAAE,OAAO,MAAM,CAAC,cAAc,QAAQ,UAAU;IACtE,IAAI,QAAQ,YAAY,EAAE,OAAO,MAAM,CAAC,gBAAgB,QAAQ,YAAY;IAC5E,IAAI,QAAQ,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;IACpD,IAAI,QAAQ,aAAa,EAAE,OAAO,MAAM,CAAC,iBAAiB;IAC1D,IAAI,OAAO,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAEjD,MAAM,cAAc,OAAO,QAAQ;IACnC,MAAM,WAAW,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE7E,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;AACrC;AAKO,eAAe,kBACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;AACrE;AAKO,eAAe,kBACpB,UAAkB;IAElB,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;AACrC;AAKO,eAAe,iBACpB,UAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACnC,YACA;IAEF,OAAO,SAAS,OAAO;AACzB;AAKO,eAAe,wBACpB,UAAkB,EAClB,OAAe,EACf,QAGC;IAED,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,CAAC,mBAAmB,EAAE,QAAQ,UAAU,CAAC,EAAE;QAC5E,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,WAAmC;QACvC,OAAO;QACP,eAAe;QACf,UAAU;QACV,gBAAgB;IAClB;IACA,OAAO,QAAQ,CAAC,UAAU,IAAI;AAChC;AAKO,SAAS,oBAAoB,WAAmB;IACrD,MAAM,WAAmC;QACvC,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,OAAO,QAAQ,CAAC,YAAY,IAAI;AAClC;AAKO,SAAS,oBAAoB,UAAkB;IACpD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,GAAG,WAAW,SAAS,CAAC;IACjC,OAAO,IAAI,aAAa,IAAI;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO;QACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;AACF", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/features/changelog/components/server/changelog-list.tsx"], "sourcesContent": ["/**\n * Changelog List - Business-friendly changelog entries display.\n * \n * Server component that renders changelog entries in the table format\n * specified in their verbiage guidelines.\n */\n\nimport { getChangelogEntries, getEntryTypeEmoji, getImpactLevelColor, formatPublishedDate } from \"@/lib/api/changelog\";\nimport Link from \"next/link\";\n\ninterface ChangelogListProps {\n  tenantSlug: string;\n  filters: {\n    entry_type?: string;\n    impact_level?: string;\n    tags?: string;\n    featured_only?: string;\n    page?: string;\n  };\n}\n\nexport async function ChangelogList({ tenantSlug, filters }: ChangelogListProps) {\n  let entries;\n  \n  try {\n    const page = filters.page ? parseInt(filters.page) : 1;\n    const response = await getChangelogEntries(tenantSlug, {\n      entry_type: filters.entry_type,\n      impact_level: filters.impact_level,\n      tags: filters.tags,\n      featured_only: filters.featured_only === 'true',\n    }, page);\n    \n    entries = response.results;\n  } catch (error) {\n    return (\n      <div className=\"bg-white rounded-lg border border-gray-200 p-8 text-center\">\n        <div className=\"text-gray-500\">\n          <div className=\"text-4xl mb-4\">📝</div>\n          <h3 className=\"text-lg font-medium mb-2\">Unable to load changelog</h3>\n          <p className=\"text-sm\">Please try again later or contact support if the problem persists.</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!entries || entries.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg border border-gray-200 p-8 text-center\">\n        <div className=\"text-gray-500\">\n          <div className=\"text-4xl mb-4\">🎉</div>\n          <h3 className=\"text-lg font-medium mb-2\">You're all caught up!</h3>\n          <p className=\"text-sm\">No new updates match your current filters. Check back soon for more improvements.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n      {/* Table header following their verbiage format */}\n      <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\n        <div className=\"grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700\">\n          <div className=\"col-span-5\">What Changed</div>\n          <div className=\"col-span-4\">Why You'll Love It</div>\n          <div className=\"col-span-2\">What To Do</div>\n          <div className=\"col-span-1 text-center\">Impact</div>\n        </div>\n      </div>\n      \n      {/* Changelog entries */}\n      <div className=\"divide-y divide-gray-200\">\n        {entries.map((entry) => (\n          <Link\n            key={entry.id}\n            href={`/${tenantSlug}/changelog/${entry.id}`}\n            className=\"block hover:bg-gray-50 transition-colors duration-150\"\n          >\n            <div className=\"px-6 py-6\">\n              <div className=\"grid grid-cols-12 gap-4 items-start\">\n                {/* What Changed */}\n                <div className=\"col-span-5\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"text-2xl flex-shrink-0 mt-1\">\n                      {getEntryTypeEmoji(entry.entry_type)}\n                    </div>\n                    <div className=\"min-w-0 flex-1\">\n                      <h3 className=\"font-semibold text-gray-900 text-lg leading-tight\">\n                        {entry.title}\n                        {entry.is_featured && (\n                          <span className=\"ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800\">\n                            Featured\n                          </span>\n                        )}\n                      </h3>\n                      <div className=\"mt-2 flex items-center space-x-4 text-sm text-gray-500\">\n                        <span>{formatPublishedDate(entry.published_date)}</span>\n                        {entry.author_name && (\n                          <span>by {entry.author_name}</span>\n                        )}\n                        {entry.media_count > 0 && (\n                          <span className=\"flex items-center space-x-1\">\n                            <span>📸</span>\n                            <span>{entry.media_count} media</span>\n                          </span>\n                        )}\n                      </div>\n                      {entry.tags.length > 0 && (\n                        <div className=\"mt-2 flex flex-wrap gap-1\">\n                          {entry.tags.map((tag) => (\n                            <span\n                              key={tag.id}\n                              className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\"\n                              style={{\n                                backgroundColor: `${tag.color}20`,\n                                color: tag.color,\n                                borderColor: `${tag.color}40`,\n                                borderWidth: '1px'\n                              }}\n                            >\n                              {tag.name}\n                            </span>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Why You'll Love It */}\n                <div className=\"col-span-4\">\n                  <p className=\"text-gray-700 leading-relaxed\">\n                    {entry.business_benefit}\n                  </p>\n                  {entry.revenue_impact && (\n                    <div className=\"mt-2 text-sm font-medium text-green-600\">\n                      💰 {entry.revenue_impact}\n                    </div>\n                  )}\n                  {entry.social_proof && (\n                    <div className=\"mt-1 text-sm text-blue-600\">\n                      👥 {entry.social_proof}\n                    </div>\n                  )}\n                  {entry.competitive_advantage && (\n                    <div className=\"mt-1 text-sm text-purple-600\">\n                      🎯 {entry.competitive_advantage}\n                    </div>\n                  )}\n                </div>\n                \n                {/* What To Do */}\n                <div className=\"col-span-2\">\n                  <p className=\"text-gray-700 text-sm leading-relaxed\">\n                    {entry.action_required}\n                  </p>\n                  {entry.feedback_summary.total > 0 && (\n                    <div className=\"mt-2 text-xs text-gray-500\">\n                      {entry.feedback_summary.helpful} found helpful\n                    </div>\n                  )}\n                </div>\n                \n                {/* Impact Level */}\n                <div className=\"col-span-1 text-center\">\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getImpactLevelColor(entry.impact_level)}`}>\n                    {entry.impact_level_display}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;;;;AAaO,eAAe,cAAc,EAAE,UAAU,EAAE,OAAO,EAAsB;IAC7E,IAAI;IAEJ,IAAI;QACF,MAAM,OAAO,QAAQ,IAAI,GAAG,SAAS,QAAQ,IAAI,IAAI;QACrD,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;YACrD,YAAY,QAAQ,UAAU;YAC9B,cAAc,QAAQ,YAAY;YAClC,MAAM,QAAQ,IAAI;YAClB,eAAe,QAAQ,aAAa,KAAK;QAC3C,GAAG;QAEH,UAAU,SAAS,OAAO;IAC5B,EAAE,OAAO,OAAO;QACd,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6WAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6WAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAI/B;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6WAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6WAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAI/B;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCAAa;;;;;;sCAC5B,6WAAC;4BAAI,WAAU;sCAAa;;;;;;sCAC5B,6WAAC;4BAAI,WAAU;sCAAa;;;;;;sCAC5B,6WAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;0BAK5C,6WAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,sBACZ,6WAAC,2RAAA,CAAA,UAAI;wBAEH,MAAM,CAAC,CAAC,EAAE,WAAW,WAAW,EAAE,MAAM,EAAE,EAAE;wBAC5C,WAAU;kCAEV,cAAA,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDAEb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,UAAU;;;;;;8DAErC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAG,WAAU;;gEACX,MAAM,KAAK;gEACX,MAAM,WAAW,kBAChB,6WAAC;oEAAK,WAAU;8EAAyG;;;;;;;;;;;;sEAK7H,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;8EAAM,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,cAAc;;;;;;gEAC9C,MAAM,WAAW,kBAChB,6WAAC;;wEAAK;wEAAI,MAAM,WAAW;;;;;;;gEAE5B,MAAM,WAAW,GAAG,mBACnB,6WAAC;oEAAK,WAAU;;sFACd,6WAAC;sFAAK;;;;;;sFACN,6WAAC;;gFAAM,MAAM,WAAW;gFAAC;;;;;;;;;;;;;;;;;;;wDAI9B,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6WAAC;4DAAI,WAAU;sEACZ,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,oBACf,6WAAC;oEAEC,WAAU;oEACV,OAAO;wEACL,iBAAiB,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;wEACjC,OAAO,IAAI,KAAK;wEAChB,aAAa,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;wEAC7B,aAAa;oEACf;8EAEC,IAAI,IAAI;mEATJ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAmBzB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAE,WAAU;0DACV,MAAM,gBAAgB;;;;;;4CAExB,MAAM,cAAc,kBACnB,6WAAC;gDAAI,WAAU;;oDAA0C;oDACnD,MAAM,cAAc;;;;;;;4CAG3B,MAAM,YAAY,kBACjB,6WAAC;gDAAI,WAAU;;oDAA6B;oDACtC,MAAM,YAAY;;;;;;;4CAGzB,MAAM,qBAAqB,kBAC1B,6WAAC;gDAAI,WAAU;;oDAA+B;oDACxC,MAAM,qBAAqB;;;;;;;;;;;;;kDAMrC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAE,WAAU;0DACV,MAAM,eAAe;;;;;;4CAEvB,MAAM,gBAAgB,CAAC,KAAK,GAAG,mBAC9B,6WAAC;gDAAI,WAAU;;oDACZ,MAAM,gBAAgB,CAAC,OAAO;oDAAC;;;;;;;;;;;;;kDAMtC,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAK,WAAW,CAAC,2EAA2E,EAAE,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,YAAY,GAAG;sDACrI,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;uBA5F9B,MAAM,EAAE;;;;;;;;;;;;;;;;AAsGzB", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/features/changelog/components/server/changelog-header.tsx"], "sourcesContent": ["/**\n * Changelog Header - Business-friendly header with stats and description.\n * \n * Server component that displays changelog overview with psychological\n * warfare elements and business-focused messaging.\n */\n\nimport { getChangelogStats } from \"@/lib/api/changelog\";\n\ninterface ChangelogHeaderProps {\n  tenantSlug: string;\n}\n\nexport async function ChangelogHeader({ tenantSlug }: ChangelogHeaderProps) {\n  let stats;\n  \n  try {\n    stats = await getChangelogStats(tenantSlug);\n  } catch (error) {\n    // Graceful fallback if stats fail to load\n    stats = null;\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n      <div className=\"flex items-start justify-between\">\n        <div className=\"space-y-2\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            What's New\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl\">\n            Stay ahead of your competition with our latest features and improvements. \n            Every update is designed to give your business an unfair advantage.\n          </p>\n        </div>\n        \n        {stats && (\n          <div className=\"flex space-x-6 text-center\">\n            <div className=\"bg-blue-50 rounded-lg p-4 min-w-[100px]\">\n              <div className=\"text-2xl font-bold text-blue-600\">\n                {stats.total_entries}\n              </div>\n              <div className=\"text-sm text-blue-600 font-medium\">\n                Total Updates\n              </div>\n            </div>\n            \n            <div className=\"bg-green-50 rounded-lg p-4 min-w-[100px]\">\n              <div className=\"text-2xl font-bold text-green-600\">\n                {stats.recent_count}\n              </div>\n              <div className=\"text-sm text-green-600 font-medium\">\n                This Month\n              </div>\n            </div>\n            \n            {stats.featured_entries > 0 && (\n              <div className=\"bg-purple-50 rounded-lg p-4 min-w-[100px]\">\n                <div className=\"text-2xl font-bold text-purple-600\">\n                  {stats.featured_entries}\n                </div>\n                <div className=\"text-sm text-purple-600 font-medium\">\n                  Featured\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Business-focused value proposition */}\n      <div className=\"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"text-2xl\">🚀</div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900\">\n              Your Competitive Edge Keeps Growing\n            </h3>\n            <p className=\"text-gray-600 text-sm\">\n              Each update makes your business more powerful, more efficient, and harder for competitors to match. \n              You're not just using software—you're building an unfair advantage.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;;;AAMO,eAAe,gBAAgB,EAAE,UAAU,EAAwB;IACxE,IAAI;IAEJ,IAAI;QACF,QAAQ,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;IAClC,EAAE,OAAO,OAAO;QACd,0CAA0C;QAC1C,QAAQ;IACV;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6WAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;oBAMhD,uBACC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa;;;;;;kDAEtB,6WAAC;wCAAI,WAAU;kDAAoC;;;;;;;;;;;;0CAKrD,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACZ,MAAM,YAAY;;;;;;kDAErB,6WAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;4BAKrD,MAAM,gBAAgB,GAAG,mBACxB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACZ,MAAM,gBAAgB;;;;;;kDAEzB,6WAAC;wCAAI,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;0BAU/D,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCAAW;;;;;;sCAC1B,6WAAC;;8CACC,6WAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAG5C,6WAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/features/changelog/components/client/changelog-filters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ChangelogFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call ChangelogFilters() from the server but ChangelogFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/changelog/components/client/changelog-filters.tsx <module evaluation>\",\n    \"ChangelogFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gGACA", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/features/changelog/components/client/changelog-filters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ChangelogFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call ChangelogFilters() from the server but ChangelogFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/features/changelog/components/client/changelog-filters.tsx\",\n    \"ChangelogFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,4EACA", "debugId": null}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/app/%5BtenantSlug%5D/changelog/page.tsx"], "sourcesContent": ["/**\n * Changelog Page - Business-friendly changelog display.\n * \n * Following their established patterns with server-side rendering,\n * tenant-aware routing, and business-focused presentation.\n */\n\nimport { DashboardLayout } from \"@/components/dashboard/dashboard-layout\";\nimport { ChangelogList } from \"@/features/changelog/components/server/changelog-list\";\nimport { ChangelogHeader } from \"@/features/changelog/components/server/changelog-header\";\nimport { ChangelogFilters } from \"@/features/changelog/components/client/changelog-filters\";\n\ninterface ChangelogPageProps {\n  params: Promise<{\n    tenantSlug: string;\n  }>;\n  searchParams: Promise<{\n    entry_type?: string;\n    impact_level?: string;\n    tags?: string;\n    featured_only?: string;\n    page?: string;\n  }>;\n}\n\nexport default async function ChangelogPage({ \n  params, \n  searchParams \n}: ChangelogPageProps) {\n  const { tenantSlug } = await params;\n  const filters = await searchParams;\n\n  return (\n    <DashboardLayout tenantSlug={tenantSlug}>\n      <div className=\"space-y-6\">\n        {/* Header with title and description */}\n        <ChangelogHeader tenantSlug={tenantSlug} />\n        \n        {/* Filters */}\n        <ChangelogFilters \n          tenantSlug={tenantSlug}\n          currentFilters={filters}\n        />\n        \n        {/* Changelog entries list */}\n        <ChangelogList \n          tenantSlug={tenantSlug}\n          filters={filters}\n        />\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAED;AACA;AACA;AACA;;;;;;AAee,eAAe,cAAc,EAC1C,MAAM,EACN,YAAY,EACO;IACnB,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;IAC7B,MAAM,UAAU,MAAM;IAEtB,qBACE,6WAAC,sJAAA,CAAA,kBAAe;QAAC,YAAY;kBAC3B,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,4KAAA,CAAA,kBAAe;oBAAC,YAAY;;;;;;8BAG7B,6WAAC,6KAAA,CAAA,mBAAgB;oBACf,YAAY;oBACZ,gBAAgB;;;;;;8BAIlB,6WAAC,0KAAA,CAAA,gBAAa;oBACZ,YAAY;oBACZ,SAAS;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}]}