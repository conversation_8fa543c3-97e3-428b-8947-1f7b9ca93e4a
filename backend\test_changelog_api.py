#!/usr/bin/env python
"""
Test script for changelog API endpoints.
Tests the multi-tenant changelog system with sample data.
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"
TENANT_SLUG = "testclient"

def test_changelog_endpoints():
    """Test all changelog API endpoints."""
    
    print("🧪 Testing Changelog API Endpoints")
    print("=" * 50)
    
    # Test 1: Get changelog entries
    print("\n1. Testing GET /api/{tenant}/changelog/entries/")
    url = f"{BASE_URL}/api/{TENANT_SLUG}/changelog/entries/"
    
    try:
        response = requests.get(url)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {len(data.get('results', []))} entries")
            
            # Show first entry details
            if data.get('results'):
                first_entry = data['results'][0]
                print(f"   First entry: {first_entry.get('title', 'No title')}")
                print(f"   Type: {first_entry.get('entry_type_display', 'Unknown')}")
                print(f"   Impact: {first_entry.get('impact_level_display', 'Unknown')}")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Get changelog tags
    print("\n2. Testing GET /api/{tenant}/changelog/tags/")
    url = f"{BASE_URL}/api/{TENANT_SLUG}/changelog/tags/"
    
    try:
        response = requests.get(url)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            tags = data.get('results', [])
            print(f"✅ Success! Found {len(tags)} tags")
            
            for tag in tags[:3]:  # Show first 3 tags
                print(f"   - {tag.get('name', 'No name')} ({tag.get('color', 'No color')})")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Get changelog stats
    print("\n3. Testing GET /api/{tenant}/changelog/entries/stats/")
    url = f"{BASE_URL}/api/{TENANT_SLUG}/changelog/entries/stats/"
    
    try:
        response = requests.get(url)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Stats retrieved")
            print(f"   Total entries: {data.get('total_entries', 0)}")
            print(f"   Featured entries: {data.get('featured_entries', 0)}")
            print(f"   Recent entries: {data.get('recent_count', 0)}")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Test filtering
    print("\n4. Testing filtering by entry type")
    url = f"{BASE_URL}/api/{TENANT_SLUG}/changelog/entries/?entry_type=new"
    
    try:
        response = requests.get(url)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {len(data.get('results', []))} 'new' entries")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 5: Test individual entry detail
    print("\n5. Testing individual entry detail")
    
    # First get an entry ID
    try:
        response = requests.get(f"{BASE_URL}/api/{TENANT_SLUG}/changelog/entries/")
        if response.status_code == 200:
            data = response.json()
            if data.get('results'):
                entry_id = data['results'][0]['id']
                
                # Now test the detail endpoint
                detail_url = f"{BASE_URL}/api/{TENANT_SLUG}/changelog/entries/{entry_id}/"
                detail_response = requests.get(detail_url)
                
                print(f"Status: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    entry_data = detail_response.json()
                    print(f"✅ Success! Entry detail retrieved")
                    print(f"   Title: {entry_data.get('title', 'No title')}")
                    print(f"   Business benefit: {entry_data.get('business_benefit', 'No benefit')[:100]}...")
                    print(f"   Action required: {entry_data.get('action_required', 'No action')[:100]}...")
                else:
                    print(f"❌ Failed: {detail_response.text}")
            else:
                print("❌ No entries found to test detail view")
        else:
            print("❌ Could not get entries list for detail test")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Changelog API testing complete!")
    print("\nNext steps:")
    print("1. Start the Next.js frontend: cd frontend && npm run dev")
    print(f"2. Visit: http://localhost:3000/{TENANT_SLUG}/changelog")
    print("3. Test the full user experience!")

if __name__ == "__main__":
    test_changelog_endpoints()
