/**
 * Changelog List - Business-friendly changelog entries display.
 * 
 * Server component that renders changelog entries in the table format
 * specified in their verbiage guidelines.
 */

import { getChangelogEntries, getEntryTypeEmoji, getImpactLevelColor, formatPublishedDate } from "@/lib/api/changelog";
import Link from "next/link";

interface ChangelogListProps {
  tenantSlug: string;
  filters: {
    entry_type?: string;
    impact_level?: string;
    tags?: string;
    featured_only?: string;
    page?: string;
  };
}

export async function ChangelogList({ tenantSlug, filters }: ChangelogListProps) {
  let entries;
  
  try {
    const page = filters.page ? parseInt(filters.page) : 1;
    const response = await getChangelogEntries(tenantSlug, {
      entry_type: filters.entry_type,
      impact_level: filters.impact_level,
      tags: filters.tags,
      featured_only: filters.featured_only === 'true',
    }, page);
    
    entries = response.results;
  } catch (error) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <div className="text-gray-500">
          <div className="text-4xl mb-4">📝</div>
          <h3 className="text-lg font-medium mb-2">Unable to load changelog</h3>
          <p className="text-sm">Please try again later or contact support if the problem persists.</p>
        </div>
      </div>
    );
  }

  if (!entries || entries.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <div className="text-gray-500">
          <div className="text-4xl mb-4">🎉</div>
          <h3 className="text-lg font-medium mb-2">You're all caught up!</h3>
          <p className="text-sm">No new updates match your current filters. Check back soon for more improvements.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Table header following their verbiage format */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
          <div className="col-span-5">What Changed</div>
          <div className="col-span-4">Why You'll Love It</div>
          <div className="col-span-2">What To Do</div>
          <div className="col-span-1 text-center">Impact</div>
        </div>
      </div>
      
      {/* Changelog entries */}
      <div className="divide-y divide-gray-200">
        {entries.map((entry) => (
          <Link
            key={entry.id}
            href={`/${tenantSlug}/changelog/${entry.id}`}
            className="block hover:bg-gray-50 transition-colors duration-150"
          >
            <div className="px-6 py-6">
              <div className="grid grid-cols-12 gap-4 items-start">
                {/* What Changed */}
                <div className="col-span-5">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl flex-shrink-0 mt-1">
                      {getEntryTypeEmoji(entry.entry_type)}
                    </div>
                    <div className="min-w-0 flex-1">
                      <h3 className="font-semibold text-gray-900 text-lg leading-tight">
                        {entry.title}
                        {entry.is_featured && (
                          <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Featured
                          </span>
                        )}
                      </h3>
                      <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                        <span>{formatPublishedDate(entry.published_date)}</span>
                        {entry.author_name && (
                          <span>by {entry.author_name}</span>
                        )}
                        {entry.media_count > 0 && (
                          <span className="flex items-center space-x-1">
                            <span>📸</span>
                            <span>{entry.media_count} media</span>
                          </span>
                        )}
                      </div>
                      {entry.tags.length > 0 && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {entry.tags.map((tag) => (
                            <span
                              key={tag.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                              style={{
                                backgroundColor: `${tag.color}20`,
                                color: tag.color,
                                borderColor: `${tag.color}40`,
                                borderWidth: '1px'
                              }}
                            >
                              {tag.name}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Why You'll Love It */}
                <div className="col-span-4">
                  <p className="text-gray-700 leading-relaxed">
                    {entry.business_benefit}
                  </p>
                  {entry.revenue_impact && (
                    <div className="mt-2 text-sm font-medium text-green-600">
                      💰 {entry.revenue_impact}
                    </div>
                  )}
                  {entry.social_proof && (
                    <div className="mt-1 text-sm text-blue-600">
                      👥 {entry.social_proof}
                    </div>
                  )}
                  {entry.competitive_advantage && (
                    <div className="mt-1 text-sm text-purple-600">
                      🎯 {entry.competitive_advantage}
                    </div>
                  )}
                </div>
                
                {/* What To Do */}
                <div className="col-span-2">
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {entry.action_required}
                  </p>
                  {entry.feedback_summary.total > 0 && (
                    <div className="mt-2 text-xs text-gray-500">
                      {entry.feedback_summary.helpful} found helpful
                    </div>
                  )}
                </div>
                
                {/* Impact Level */}
                <div className="col-span-1 text-center">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getImpactLevelColor(entry.impact_level)}`}>
                    {entry.impact_level_display}
                  </span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
