/**
 * Changelog Entry Detail Page - Individual changelog entry view.
 * 
 * Displays detailed information about a specific changelog entry
 * with media, feedback options, and business-friendly formatting.
 */

import { DashboardLayout } from "@/components/dashboard/dashboard-layout";
import { ChangelogEntryDetail } from "@/features/changelog/components/server/changelog-entry-detail";
import { ChangelogEntryFeedback } from "@/features/changelog/components/client/changelog-entry-feedback";
import { getChangelogEntry } from "@/lib/api/changelog";
import { notFound } from "next/navigation";

interface ChangelogEntryPageProps {
  params: Promise<{
    tenantSlug: string;
    id: string;
  }>;
}

export default async function ChangelogEntryPage({ params }: ChangelogEntryPageProps) {
  const { tenantSlug, id } = await params;
  
  try {
    const entry = await getChangelogEntry(tenantSlug, parseInt(id));
    
    return (
      <DashboardLayout tenantSlug={tenantSlug}>
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Entry detail content */}
          <ChangelogEntryDetail entry={entry} />
          
          {/* Feedback section */}
          <ChangelogEntryFeedback 
            tenantSlug={tenantSlug}
            entryId={entry.id}
          />
        </div>
      </DashboardLayout>
    );
  } catch (error) {
    // Entry not found or access denied
    notFound();
  }
}
