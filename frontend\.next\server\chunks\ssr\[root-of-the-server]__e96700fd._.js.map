{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/tenant-client-provider.tsx"], "sourcesContent": ["// components/providers/tenant-client-provider.tsx - FIXED\r\n\"use client\";\r\n\r\nimport { createContext, useContext, ReactNode } from \"react\";\r\nimport { TenantContextValue } from \"@/types/tenant\";\r\n\r\nconst TenantContext = createContext<TenantContextValue | null>(null);\r\n\r\ninterface TenantClientProviderProps {\r\n  children: ReactNode;\r\n  value: TenantContextValue;\r\n}\r\n\r\n/**\r\n * Client-side tenant context provider\r\n * Receives validated tenant context from server component\r\n */\r\nexport function TenantClientProvider({\r\n  children,\r\n  value,\r\n}: TenantClientProviderProps) {\r\n  return (\r\n    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>\r\n  );\r\n}\r\n\r\n// ✅ FIXED: Enhanced hook with client-side hasAccess function\r\nexport function useTenant() {\r\n  const context = useContext(TenantContext);\r\n\r\n  if (!context) {\r\n    throw new Error(\"useTenant must be used within a TenantProvider\");\r\n  }\r\n\r\n  // ✅ FIXED: Create hasAccess function on client side using server data\r\n  const hasAccess = (resource: string): boolean => {\r\n    // Use the permissions array passed from server\r\n    return context.permissions.includes(resource);\r\n  };\r\n\r\n  // Return context with client-side hasAccess function\r\n  return {\r\n    ...context,\r\n    hasAccess, // ← Function created on client side\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for tenant-aware API calls\r\n * Automatically includes tenant context in requests\r\n */\r\nexport function useTenantApi() {\r\n  const { tenantSlug, session } = useTenant();\r\n\r\n  async function fetchTenantData<T>(\r\n    endpoint: string,\r\n    options: RequestInit = {}\r\n  ): Promise<T> {\r\n    const url = `${process.env.NEXT_PUBLIC_API_URL}/api/${tenantSlug}${endpoint}`;\r\n\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      credentials: \"include\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"X-Tenant-Slug\": tenantSlug,\r\n        ...options.headers,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      if (response.status === 401) {\r\n        window.location.href = `/login?redirect=/${tenantSlug}`;\r\n        throw new Error(\"Authentication required\");\r\n      }\r\n\r\n      const errorData = await response.json().catch(() => ({}));\r\n      throw new Error(\r\n        errorData.message || `HTTP error! status: ${response.status}`\r\n      );\r\n    }\r\n\r\n    return response.json();\r\n  }\r\n\r\n  return { fetchTenantData, tenantSlug, session };\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAG1D;AAFA;;;AAKA,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD,EAA6B;AAWxD,SAAS,qBAAqB,EACnC,QAAQ,EACR,KAAK,EACqB;IAC1B,qBACE,6WAAC,cAAc,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAE3C;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,sEAAsE;IACtE,MAAM,YAAY,CAAC;QACjB,+CAA+C;QAC/C,OAAO,QAAQ,WAAW,CAAC,QAAQ,CAAC;IACtC;IAEA,qDAAqD;IACrD,OAAO;QACL,GAAG,OAAO;QACV;IACF;AACF;AAMO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG;IAEhC,eAAe,gBACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;QAEzB,MAAM,MAAM,6DAAmC,KAAK,EAAE,aAAa,UAAU;QAE7E,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,aAAa;YACb,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,iBAAiB,EAAE,YAAY;gBACvD,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAEjE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;QAAE;QAAiB;QAAY;IAAQ;AAChD", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/auth-config.ts"], "sourcesContent": ["// lib/auth-config.ts - Clean enterprise configuration\nexport const AUTH_CONFIG = {\n  API_BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',\n} as const;\n\n// Enterprise session configuration\nexport const SESSION_CONFIG = {\n  COOKIE_NAME: 'auth_token',\n  MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours in milliseconds\n  SECURE: process.env.NODE_ENV === 'production',\n  SAME_SITE: 'lax' as const,\n} as const;\n  \n  // lib/api-client.ts\n  interface AuthResponse {\n    token: string\n    user: {\n      id: number\n      email: string\n      firstName: string\n      lastName: string\n      role: string\n      createdAt: string\n      updatedAt: string\n    }\n    client: {\n      id: number\n      slug: string\n      name: string\n      websiteUrl: string\n      industry: string\n    }\n  }\n  \n  interface LoginData {\n    email: string\n    password: string\n  }\n  \n  interface RegisterData {\n    first_name: string\n    last_name: string\n    email: string\n    password: string\n    company_name: string\n    website_url?: string\n    industry?: string\n  }\n  \n  class ApiClient {\n    private baseUrl: string\n  \n    constructor(baseUrl: string) {\n      this.baseUrl = baseUrl\n    }\n  \n    private async request<T>(\n      endpoint: string,\n      options: RequestInit = {}\n    ): Promise<T> {\n      const url = `${this.baseUrl}${endpoint}`\n      \n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      }\n  \n      // Add auth token if available\n      if (typeof window !== 'undefined') {\n        const token = localStorage.getItem(AUTH_CONFIG.API_BASE_URL)\n        if (token) {\n          config.headers = {\n            ...config.headers,\n            Authorization: `Token ${token}`,\n          }\n        }\n      }\n  \n      const response = await fetch(url, config)\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`)\n      }\n  \n      return response.json()\n    }\n  \n    async login(data: LoginData): Promise<AuthResponse> {\n      return this.request<AuthResponse>('/api/auth/login/', {\n        method: 'POST',\n        body: JSON.stringify(data),\n      })\n    }\n  \n    async register(data: RegisterData): Promise<AuthResponse> {\n      return this.request<AuthResponse>('/api/auth/register/', {\n        method: 'POST',\n        body: JSON.stringify(data),\n      })\n    }\n  \n    async logout(): Promise<void> {\n      await this.request('/api/auth/logout/', {\n        method: 'POST',\n      })\n    }\n  \n    async refreshToken(): Promise<{ token: string }> {\n      return this.request<{ token: string }>('/api/auth/refresh/', {\n        method: 'POST',\n      })\n    }\n  }\n  \n  export const apiClient = new ApiClient(AUTH_CONFIG.API_BASE_URL)\n  "], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AAC/C,MAAM,cAAc;IACzB,cAAc,6DAAmC;AACnD;AAGO,MAAM,iBAAiB;IAC5B,aAAa;IACb,SAAS,KAAK,KAAK,KAAK;IACxB,QAAQ,oDAAyB;IACjC,WAAW;AACb;AAsCE,MAAM;IACI,QAAe;IAEvB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,8BAA8B;QAC9B;;QAUA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC9E;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAM,IAAe,EAAyB;QAClD,OAAO,IAAI,CAAC,OAAO,CAAe,oBAAoB;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAS,IAAkB,EAAyB;QACxD,OAAO,IAAI,CAAC,OAAO,CAAe,uBAAuB;YACvD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAwB;QAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB;YACtC,QAAQ;QACV;IACF;IAEA,MAAM,eAA2C;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAoB,sBAAsB;YAC3D,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI,UAAU,YAAY,YAAY", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/utils/correlation.ts"], "sourcesContent": ["// lib/utils/correlation.ts - Request tracing utility (Under 25 lines)\nimport { v4 as uuidv4 } from 'uuid';\n\n/**\n * Generates correlation IDs for cross-stack request tracing\n * Essential for debugging multi-tenant API calls\n */\nexport function generateCorrelationId(): string {\n  return `req_${uuidv4().substring(0, 8)}_${Date.now()}`;\n}\n\n/**\n * Extracts correlation ID from response headers\n */\nexport function getCorrelationId(response: Response): string | null {\n  return response.headers.get('X-Correlation-ID');\n}\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;AACtE;;AAMO,SAAS;IACd,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,4NAAA,CAAA,KAAM,AAAD,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI;AACxD;AAKO,SAAS,iBAAiB,QAAkB;IACjD,OAAO,SAAS,OAAO,CAAC,GAAG,CAAC;AAC9B", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/auth/client.ts"], "sourcesContent": ["// lib/auth/client.ts - Pure HTTP-only cookie authentication\r\n'use client';\r\n\r\nimport { useRouter } from 'next/navigation';\r\nimport { AUTH_CONFIG } from '../auth-config';\r\nimport { generateCorrelationId } from '../utils/correlation';\r\n\r\ninterface LoginCredentials {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\ninterface RegisterData {\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  password: string;\r\n  company_name: string;\r\n  website_url?: string;\r\n  industry?: string;\r\n}\r\n\r\nexport class AuthClient {\r\n  async login(credentials: LoginCredentials): Promise<{ success: boolean; redirectTo?: string }> {\r\n    const correlationId = generateCorrelationId();\r\n    \r\n    try {\r\n      const response = await fetch(`${AUTH_CONFIG.API_BASE_URL}/api/auth/login/`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'X-Correlation-ID': correlationId,\r\n        },\r\n        credentials: 'include', // ✅ HTTP-only cookies only\r\n        body: JSON.stringify(credentials),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.message || 'Login failed');\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Django sets HTTP-only cookies automatically\r\n      console.log(`[${correlationId}] Login successful for tenant: ${data.client.slug}`);\r\n\r\n      // Check onboarding status to determine redirect\r\n      try {\r\n        const statusResponse = await fetch(`${AUTH_CONFIG.API_BASE_URL}/api/${data.client.slug}/onboarding/status/`, {\r\n          headers: {\r\n            'Authorization': `Token ${data.token}`,\r\n            'Content-Type': 'application/json',\r\n            'X-Correlation-ID': correlationId,\r\n          },\r\n          credentials: 'include',\r\n        });\r\n\r\n        if (statusResponse.ok) {\r\n          const statusData = await statusResponse.json();\r\n          const redirectTo = statusData.isComplete\r\n            ? `/${data.client.slug}/dashboard`\r\n            : `/${data.client.slug}/onboarding`;\r\n\r\n          console.log(`[${correlationId}] Redirecting to: ${redirectTo} (onboarding complete: ${statusData.isComplete})`);\r\n\r\n          return {\r\n            success: true,\r\n            redirectTo\r\n          };\r\n        }\r\n      } catch (error) {\r\n        console.warn(`[${correlationId}] Failed to check onboarding status, defaulting to dashboard:`, error);\r\n      }\r\n\r\n      // Fallback to dashboard if status check fails\r\n      return {\r\n        success: true,\r\n        redirectTo: `/${data.client.slug}/dashboard`\r\n      };\r\n    } catch (error) {\r\n      console.error(`[${correlationId}] Login failed:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async register(data: RegisterData): Promise<{ success: boolean; redirectTo?: string }> {\r\n    const correlationId = generateCorrelationId();\r\n    \r\n    try {\r\n      const response = await fetch(`${AUTH_CONFIG.API_BASE_URL}/api/auth/register/`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'X-Correlation-ID': correlationId,\r\n        },\r\n        credentials: 'include',\r\n        body: JSON.stringify(data),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.message || 'Registration failed');\r\n      }\r\n\r\n      const responseData = await response.json();\r\n      \r\n      console.log(`[${correlationId}] Registration successful for tenant: ${responseData.client.slug}`);\r\n      \r\n      return {\r\n        success: true,\r\n        redirectTo: `/${responseData.client.slug}/onboarding`\r\n      };\r\n    } catch (error) {\r\n      console.error(`[${correlationId}] Registration failed:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async logout(): Promise<void> {\r\n    const correlationId = generateCorrelationId();\r\n    \r\n    try {\r\n      await fetch(`${AUTH_CONFIG.API_BASE_URL}/api/auth/logout/`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'X-Correlation-ID': correlationId,\r\n        },\r\n        credentials: 'include',\r\n      });\r\n      \r\n      console.log(`[${correlationId}] Logout successful`);\r\n    } catch (error) {\r\n      console.error(`[${correlationId}] Logout API error:`, error);\r\n    } finally {\r\n      // Force redirect to clear all state\r\n      window.location.href = '/login';\r\n    }\r\n  }\r\n}\r\n\r\nexport const authClient = new AuthClient();\r\n\r\n/**\r\n * React hook for authentication actions\r\n */\r\nexport function useAuth() {\r\n  const router = useRouter();\r\n\r\n  const login = async (credentials: LoginCredentials) => {\r\n    const result = await authClient.login(credentials);\r\n    if (result.success && result.redirectTo) {\r\n      router.push(result.redirectTo);\r\n    }\r\n  };\r\n\r\n  const register = async (data: RegisterData) => {\r\n    const result = await authClient.register(data);\r\n    if (result.success && result.redirectTo) {\r\n      router.push(result.redirectTo);\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    await authClient.logout();\r\n  };\r\n\r\n  return { login, register, logout };\r\n}\r\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;AAG5D;AACA;AACA;AAJA;;;;AAqBO,MAAM;IACX,MAAM,MAAM,WAA6B,EAAsD;QAC7F,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;QAE1C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,cAAW,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE;gBAC1E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,oBAAoB;gBACtB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,8CAA8C;YAC9C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,+BAA+B,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE;YAEjF,gDAAgD;YAChD,IAAI;gBACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,4HAAA,CAAA,cAAW,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE;oBAC3G,SAAS;wBACP,iBAAiB,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;wBACtC,gBAAgB;wBAChB,oBAAoB;oBACtB;oBACA,aAAa;gBACf;gBAEA,IAAI,eAAe,EAAE,EAAE;oBACrB,MAAM,aAAa,MAAM,eAAe,IAAI;oBAC5C,MAAM,aAAa,WAAW,UAAU,GACpC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAChC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;oBAErC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,kBAAkB,EAAE,WAAW,uBAAuB,EAAE,WAAW,UAAU,CAAC,CAAC,CAAC;oBAE9G,OAAO;wBACL,SAAS;wBACT;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,cAAc,6DAA6D,CAAC,EAAE;YACjG;YAEA,8CAA8C;YAC9C,OAAO;gBACL,SAAS;gBACT,YAAY,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,eAAe,CAAC,EAAE;YAClD,MAAM;QACR;IACF;IAEA,MAAM,SAAS,IAAkB,EAAsD;QACrF,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;QAE1C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,cAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;gBAC7E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,oBAAoB;gBACtB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,MAAM,eAAe,MAAM,SAAS,IAAI;YAExC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,sCAAsC,EAAE,aAAa,MAAM,CAAC,IAAI,EAAE;YAEhG,OAAO;gBACL,SAAS;gBACT,YAAY,CAAC,CAAC,EAAE,aAAa,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,sBAAsB,CAAC,EAAE;YACzD,MAAM;QACR;IACF;IAEA,MAAM,SAAwB;QAC5B,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;QAE1C,IAAI;YACF,MAAM,MAAM,GAAG,4HAAA,CAAA,cAAW,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBACP,oBAAoB;gBACtB;gBACA,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,mBAAmB,CAAC;QACpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,mBAAmB,CAAC,EAAE;QACxD,SAAU;YACR,oCAAoC;YACpC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;AACF;AAEO,MAAM,aAAa,IAAI;AAKvB,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,QAAQ,OAAO;QACnB,MAAM,SAAS,MAAM,WAAW,KAAK,CAAC;QACtC,IAAI,OAAO,OAAO,IAAI,OAAO,UAAU,EAAE;YACvC,OAAO,IAAI,CAAC,OAAO,UAAU;QAC/B;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,SAAS,MAAM,WAAW,QAAQ,CAAC;QACzC,IAAI,OAAO,OAAO,IAAI,OAAO,UAAU,EAAE;YACvC,OAAO,IAAI,CAAC,OAAO,UAAU;QAC/B;IACF;IAEA,MAAM,SAAS;QACb,MAAM,WAAW,MAAM;IACzB;IAEA,OAAO;QAAE;QAAO;QAAU;IAAO;AACnC", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6WAAC,mRAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6WAAC,mRAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6WAAC,mRAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6WAAC,mRAAA,CAAA,SAA4B;kBAC3B,cAAA,6WAAC,mRAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6WAAC,mRAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6WAAC,mRAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6WAAC,mRAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,6WAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6WAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6WAAC,mRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,mRAAA,CAAA,gBAAmC;8BAClC,cAAA,6WAAC,8RAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6WAAC,mRAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6WAAC,mRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6WAAC,mRAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6WAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,8SAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6WAAC,mRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/navigation/tenant-navigation.tsx"], "sourcesContent": ["// components/navigation/tenant-navigation.tsx\r\n\"use client\";\r\n\r\nimport { useTenant } from \"@/components/providers/tenant-client-provider\";\r\nimport { useAuth } from \"@/lib/auth/client\";\r\nimport { Bell, Search, User, ChevronDown } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TenantNavigation() {\r\n  const { session, tenantSlug } = useTenant();\r\n  const { logout } = useAuth();\r\n\r\n  const handleLogout = async () => {\r\n    await logout();\r\n  };\r\n\r\n  return (\r\n    <header className='sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60'>\r\n      <div className='flex h-16 items-center justify-between px-4 md:px-6'>\r\n        {/* Left side - Brand */}\r\n        <div className='flex items-center space-x-4'>\r\n          <div className='flex items-center space-x-2'>\r\n            <div className='h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center'>\r\n              <span className='text-white font-bold text-sm'>S</span>\r\n            </div>\r\n            <div className='flex flex-col'>\r\n              <h1 className='text-sm font-semibold text-gray-900 leading-none'>\r\n                {session.client.name}\r\n              </h1>\r\n              <span className='text-xs text-gray-500'>SEO Dashboard</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right side - User actions */}\r\n        <div className='flex items-center space-x-3'>\r\n          {/* Search */}\r\n          <Button variant='ghost' size='sm' className='h-9 w-9 p-0'>\r\n            <Search className='h-4 w-4' />\r\n            <span className='sr-only'>Search</span>\r\n          </Button>\r\n\r\n          {/* Notifications */}\r\n          <Button variant='ghost' size='sm' className='h-9 w-9 p-0 relative'>\r\n            <Bell className='h-4 w-4' />\r\n            <span className='absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full'></span>\r\n            <span className='sr-only'>Notifications</span>\r\n          </Button>\r\n\r\n          {/* User menu */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant='ghost' className='h-9 px-2 py-1.5'>\r\n                <div className='flex items-center space-x-2'>\r\n                  <div className='h-6 w-6 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center'>\r\n                    <User className='h-3 w-3 text-white' />\r\n                  </div>\r\n                  <span className='text-sm font-medium text-gray-700 hidden sm:block'>\r\n                    {session.user.firstName}\r\n                  </span>\r\n                  <ChevronDown className='h-3 w-3 text-gray-500' />\r\n                </div>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent className='w-56' align='end' forceMount>\r\n              <DropdownMenuLabel className='font-normal'>\r\n                <div className='flex flex-col space-y-1'>\r\n                  <p className='text-sm font-medium leading-none'>\r\n                    {session.user.firstName} {session.user.lastName}\r\n                  </p>\r\n                  <p className='text-xs leading-none text-muted-foreground'>\r\n                    {session.user.email}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem asChild>\r\n                <Link href={`/${tenantSlug}/settings`}>\r\n                  <User className='mr-2 h-4 w-4' />\r\n                  Account Settings\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout} className='text-red-600'>\r\n                <span className='mr-2 h-4 w-4'>🚪</span>\r\n                Sign out\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAG9C;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAQA;AAdA;;;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD;IACxC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAEzB,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,qBACE,6WAAC;QAAO,WAAU;kBAChB,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDACX,QAAQ,MAAM,CAAC,IAAI;;;;;;kDAEtB,6WAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAM9C,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;;8CAC1C,6WAAC,0RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6WAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,6WAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;;8CAC1C,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6WAAC;oCAAK,WAAU;;;;;;8CAChB,6WAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,6WAAC,4IAAA,CAAA,eAAY;;8CACX,6WAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6WAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;8DACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI,CAAC,SAAS;;;;;;8DAEzB,6WAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6WAAC,4IAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDAC1D,6WAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAE,WAAU;;4DACV,QAAQ,IAAI,CAAC,SAAS;4DAAC;4DAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;kEAEjD,6WAAC;wDAAE,WAAU;kEACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;sDAIzB,6WAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,6WAAC,4IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6WAAC,2RAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,CAAC,EAAE,WAAW,SAAS,CAAC;;kEACnC,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIrC,6WAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,6WAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;4CAAc,WAAU;;8DACjD,6WAAC;oDAAK,WAAU;8DAAe;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}]}