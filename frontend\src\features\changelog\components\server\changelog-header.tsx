/**
 * Changelog Header - Business-friendly header with stats and description.
 * 
 * Server component that displays changelog overview with psychological
 * warfare elements and business-focused messaging.
 */

import { getChangelogStats } from "@/lib/api/changelog";

interface ChangelogHeaderProps {
  tenantSlug: string;
}

export async function ChangelogHeader({ tenantSlug }: ChangelogHeaderProps) {
  let stats;
  
  try {
    stats = await getChangelogStats(tenantSlug);
  } catch (error) {
    // Graceful fallback if stats fail to load
    stats = null;
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">
            What's New
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl">
            Stay ahead of your competition with our latest features and improvements. 
            Every update is designed to give your business an unfair advantage.
          </p>
        </div>
        
        {stats && (
          <div className="flex space-x-6 text-center">
            <div className="bg-blue-50 rounded-lg p-4 min-w-[100px]">
              <div className="text-2xl font-bold text-blue-600">
                {stats.total_entries}
              </div>
              <div className="text-sm text-blue-600 font-medium">
                Total Updates
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4 min-w-[100px]">
              <div className="text-2xl font-bold text-green-600">
                {stats.recent_count}
              </div>
              <div className="text-sm text-green-600 font-medium">
                This Month
              </div>
            </div>
            
            {stats.featured_entries > 0 && (
              <div className="bg-purple-50 rounded-lg p-4 min-w-[100px]">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.featured_entries}
                </div>
                <div className="text-sm text-purple-600 font-medium">
                  Featured
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Business-focused value proposition */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">🚀</div>
          <div>
            <h3 className="font-semibold text-gray-900">
              Your Competitive Edge Keeps Growing
            </h3>
            <p className="text-gray-600 text-sm">
              Each update makes your business more powerful, more efficient, and harder for competitors to match. 
              You're not just using software—you're building an unfair advantage.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
