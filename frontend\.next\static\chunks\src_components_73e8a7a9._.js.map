{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Toaster as Sonner } from \"sonner\"\r\n\r\ntype ToasterProps = React.ComponentProps<typeof Sonner>\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  return (\r\n    <Sonner\r\n      theme=\"light\"\r\n      className=\"toaster group\"\r\n      toastOptions={{\r\n        classNames: {\r\n          toast:\r\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\r\n          description: \"group-[.toast]:text-muted-foreground\",\r\n          actionButton:\r\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\r\n          cancelButton:\r\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\r\n        },\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAMA,MAAM,UAAU;QAAC,EAAE,GAAG,OAAqB;IACzC,qBACE,4TAAC,2QAAA,CAAA,UAAM;QACL,OAAM;QACN,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf;KAnBM", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/seodashboard/frontend/src/components/providers/react-query-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\r\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\r\nimport { useState } from 'react';\r\n\r\ninterface ReactQueryProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function ReactQueryProvider({ children }: ReactQueryProviderProps) {\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            // With SSR, we usually want to set some default staleTime\r\n            // above 0 to avoid refetching immediately on the client\r\n            staleTime: 60 * 1000, // 1 minute\r\n            retry: (failureCount, error: any) => {\r\n              // Don't retry on 401/403 errors\r\n              if (error?.response?.status === 401 || error?.response?.status === 403) {\r\n                return false;\r\n              }\r\n              // Retry up to 3 times for other errors\r\n              return failureCount < 3;\r\n            },\r\n          },\r\n          mutations: {\r\n            retry: false,\r\n          },\r\n        },\r\n      })\r\n  );\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n      <ReactQueryDevtools initialIsOpen={false} />\r\n    </QueryClientProvider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,mBAAmB,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;;IACjC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;uCAC3B,IACE,IAAI,yPAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,0DAA0D;wBAC1D,wDAAwD;wBACxD,WAAW,KAAK;wBAChB,KAAK;2DAAE,CAAC,cAAc;oCAEhB,iBAAmC;gCADvC,gCAAgC;gCAChC,IAAI,CAAA,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,sCAAA,gBAAiB,MAAM,MAAK,OAAO,CAAA,kBAAA,6BAAA,mBAAA,MAAO,QAAQ,cAAf,uCAAA,iBAAiB,MAAM,MAAK,KAAK;oCACtE,OAAO;gCACT;gCACA,uCAAuC;gCACvC,OAAO,eAAe;4BACxB;;oBACF;oBACA,WAAW;wBACT,OAAO;oBACT;gBACF;YACF;;IAGJ,qBACE,4TAAC,yRAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,4TAAC,2RAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC;GA/BgB;KAAA", "debugId": null}}]}